using BecaTracktion.Data;
using BecaTracktion.Data.Entities;
using BecaTracktion.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace BecaTracktion.Services
{
    /// <summary>
    /// Database-backed implementation of app verification service
    /// Replaces InMemoryAppVerificationService for production use
    /// </summary>
    public class SqlAppVerificationService : IAppVerificationService
    {
        private readonly BecaTracktionDbContext _context;
        private readonly ILogger<SqlAppVerificationService> _logger;

        public SqlAppVerificationService(BecaTracktionDbContext context, ILogger<SqlAppVerificationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret)
        {
            try
            {
                var client = await _context.ClientApplications
                    .FirstOrDefaultAsync(c => c.ClientId == clientId);

                if (client == null)
                {
                    _logger.LogWarning("Client ID not found: {ClientId}", clientId);
                    return false;
                }

                if (!client.IsActive)
                {
                    _logger.LogWarning("Client is inactive: {ClientId}", clientId);
                    return false;
                }

                // Check if client has expired
                if (client.ExpiresAt.HasValue && client.ExpiresAt.Value <= DateTime.UtcNow)
                {
                    _logger.LogWarning("Client credentials have expired: {ClientId}", clientId);
                    return false;
                }

                // Validate the secret
                var hashedSecret = HashSecret(clientSecret);
                var isValid = client.ClientSecretHash == hashedSecret;

                if (isValid)
                {
                    // Update last used timestamp
                    client.LastUsedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Client credentials validated successfully: {ClientId}", clientId);

                    // Log the authentication event
                    await LogAuditEventAsync(clientId, "Credentials Validated", 
                        JsonSerializer.Serialize(new { timestamp = DateTime.UtcNow }), 
                        "system");
                }
                else
                {
                    _logger.LogWarning("Invalid client secret for: {ClientId}", clientId);
                    
                    // Log the failed authentication attempt
                    await LogAuditEventAsync(clientId, "Invalid Credentials Attempted", 
                        JsonSerializer.Serialize(new { timestamp = DateTime.UtcNow }), 
                        "system");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating client credentials for: {ClientId}", clientId);
                return false;
            }
        }

        public async Task<(bool IsFirstRegistration, RegisteredApp RegisteredApp)> RegisterOrUpdateAppAsync(string clientId, AppMetadata metadata)
        {
            try
            {
                // Check if this app instance already exists
                var existingInstance = await _context.RegisteredAppInstances
                    .FirstOrDefaultAsync(r => r.ClientId == clientId && r.AppGuid == metadata.AppGuid);

                if (existingInstance != null)
                {
                    // Update existing registration
                    existingInstance.AppName = metadata.AppName;
                    existingInstance.AppVersion = metadata.AppVersion;
                    existingInstance.OperatingSystem = metadata.OperatingSystem;
                    existingInstance.LastUsedAt = DateTime.UtcNow;
                    existingInstance.TokensIssued++;

                    if (metadata.AdditionalProperties != null)
                    {
                        existingInstance.AdditionalProperties = JsonSerializer.Serialize(metadata.AdditionalProperties);
                    }

                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Updated existing app registration: {ClientId}:{AppGuid}", clientId, metadata.AppGuid);

                    // Convert to RegisteredApp model
                    var updatedApp = ConvertToRegisteredApp(existingInstance);
                    return (false, updatedApp);
                }
                else
                {
                    // Create new registration
                    var newInstance = new RegisteredAppInstance
                    {
                        ClientId = clientId,
                        AppGuid = metadata.AppGuid,
                        AppName = metadata.AppName,
                        AppVersion = metadata.AppVersion,
                        OperatingSystem = metadata.OperatingSystem,
                        FirstRegisteredAt = DateTime.UtcNow,
                        LastUsedAt = DateTime.UtcNow,
                        TokensIssued = 1,
                        AdditionalProperties = metadata.AdditionalProperties != null 
                            ? JsonSerializer.Serialize(metadata.AdditionalProperties) 
                            : null
                    };

                    _context.RegisteredAppInstances.Add(newInstance);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Registered new app: {ClientId}:{AppGuid}", clientId, metadata.AppGuid);

                    // Log the registration event
                    await LogAuditEventAsync(clientId, "App Instance Registered", 
                        JsonSerializer.Serialize(new { 
                            appGuid = metadata.AppGuid, 
                            appName = metadata.AppName,
                            firstRegistration = true 
                        }), 
                        "system");

                    // Convert to RegisteredApp model
                    var newApp = ConvertToRegisteredApp(newInstance);
                    return (true, newApp);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering/updating app: {ClientId}:{AppGuid}", clientId, metadata.AppGuid);
                throw;
            }
        }

        public async Task<bool> ValidateTokenBindingAsync(string clientId, AppMetadata tokenMetadata, AppMetadata currentMetadata)
        {
            try
            {
                // Validate that the token's metadata matches the current request context
                var isValid = tokenMetadata.AppGuid == currentMetadata.AppGuid &&
                             tokenMetadata.AppName == currentMetadata.AppName;

                if (!isValid)
                {
                    _logger.LogWarning("Token binding validation failed for {ClientId}. Token metadata doesn't match current context", clientId);
                    
                    // Log the failed binding validation
                    await LogAuditEventAsync(clientId, "Token Binding Validation Failed", 
                        JsonSerializer.Serialize(new { 
                            tokenAppGuid = tokenMetadata.AppGuid,
                            currentAppGuid = currentMetadata.AppGuid,
                            tokenAppName = tokenMetadata.AppName,
                            currentAppName = currentMetadata.AppName
                        }), 
                        "system");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token binding for: {ClientId}", clientId);
                return false;
            }
        }

        public async Task<RegisteredApp?> GetRegisteredAppAsync(string clientId, string appGuid)
        {
            try
            {
                var instance = await _context.RegisteredAppInstances
                    .FirstOrDefaultAsync(r => r.ClientId == clientId && r.AppGuid == appGuid);

                return instance != null ? ConvertToRegisteredApp(instance) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting registered app: {ClientId}:{AppGuid}", clientId, appGuid);
                return null;
            }
        }

        public async Task UpdateLastUsedAsync(string clientId, string appGuid)
        {
            try
            {
                var instance = await _context.RegisteredAppInstances
                    .FirstOrDefaultAsync(r => r.ClientId == clientId && r.AppGuid == appGuid);

                if (instance != null)
                {
                    instance.LastUsedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating last used timestamp: {ClientId}:{AppGuid}", clientId, appGuid);
            }
        }

        public async Task<ClientCredentials?> GetClientCredentialsAsync(string clientId)
        {
            try
            {
                var client = await _context.ClientApplications
                    .FirstOrDefaultAsync(c => c.ClientId == clientId);

                if (client == null)
                    return null;

                // Convert to ClientCredentials model
                var roles = new List<string>();
                if (!string.IsNullOrEmpty(client.AllowedRoles))
                {
                    try
                    {
                        roles = JsonSerializer.Deserialize<List<string>>(client.AllowedRoles) ?? new List<string>();
                    }
                    catch
                    {
                        // Fallback for simple string format
                        roles = new List<string> { client.AllowedRoles };
                    }
                }

                return new ClientCredentials
                {
                    ClientId = client.ClientId,
                    ClientSecretHash = client.ClientSecretHash,
                    ClientName = client.ClientName,
                    IsActive = client.IsActive,
                    CreatedAt = client.CreatedAt,
                    LastUsedAt = client.LastUsedAt,
                    AllowedRoles = roles
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client credentials: {ClientId}", clientId);
                return null;
            }
        }

        /// <summary>
        /// Hash a client secret using SHA256
        /// </summary>
        private static string HashSecret(string secret)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(secret));
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// Convert RegisteredAppInstance entity to RegisteredApp model
        /// </summary>
        private static RegisteredApp ConvertToRegisteredApp(RegisteredAppInstance instance)
        {
            var additionalProperties = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(instance.AdditionalProperties))
            {
                try
                {
                    additionalProperties = JsonSerializer.Deserialize<Dictionary<string, string>>(instance.AdditionalProperties) 
                        ?? new Dictionary<string, string>();
                }
                catch
                {
                    // If deserialization fails, leave empty
                }
            }

            return new RegisteredApp
            {
                Id = instance.Id.ToString(),
                ClientId = instance.ClientId,
                Metadata = new AppMetadata
                {
                    AppGuid = instance.AppGuid,
                    AppName = instance.AppName,
                    AppVersion = instance.AppVersion,
                    OperatingSystem = instance.OperatingSystem,
                    AdditionalProperties = additionalProperties,
                    CollectedAt = instance.FirstRegisteredAt
                },
                RegisteredAt = instance.FirstRegisteredAt,
                LastUsedAt = instance.LastUsedAt,
                IsActive = instance.IsActive,
                TokensIssued = instance.TokensIssued
            };
        }

        /// <summary>
        /// Log an audit event to the database
        /// </summary>
        private async Task LogAuditEventAsync(string? clientId, string action, string? details, string performedBy)
        {
            try
            {
                var auditEntry = new ClientAuditLog
                {
                    ClientId = clientId,
                    Action = action,
                    Details = details,
                    PerformedBy = performedBy,
                    PerformedAt = DateTime.UtcNow
                };

                _context.ClientAuditLog.Add(auditEntry);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error logging audit event: {Action} for client: {ClientId}", action, clientId);
                // Don't throw - audit logging failure shouldn't break the main operation
            }
        }
    }
}
