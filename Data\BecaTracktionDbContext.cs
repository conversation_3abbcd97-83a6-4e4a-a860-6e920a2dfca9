using BecaTracktion.Data.Entities;
using Microsoft.EntityFrameworkCore;

namespace BecaTracktion.Data
{
    /// <summary>
    /// Entity Framework DbContext for BecaTracktion client registration system
    /// </summary>
    public class BecaTracktionDbContext : DbContext
    {
        public BecaTracktionDbContext(DbContextOptions<BecaTracktionDbContext> options)
            : base(options)
        {
        }

        // DbSets for entities
        public DbSet<ClientApplication> ClientApplications { get; set; }
        public DbSet<PendingClientRegistration> PendingClientRegistrations { get; set; }
        public DbSet<RegisteredAppInstance> RegisteredAppInstances { get; set; }
        public DbSet<TelemetryUsage> TelemetryUsage { get; set; }
        public DbSet<ClientAuditLog> ClientAuditLog { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure ClientApplication
            modelBuilder.Entity<ClientApplication>(entity =>
            {
                entity.HasKey(e => e.ClientId);
                
                entity.Property(e => e.ClientId)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.ClientName)
                    .HasMaxLength(200)
                    .IsRequired();

                entity.Property(e => e.ApplicationType)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.CreatedBy)
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.ContactEmail)
                    .HasMaxLength(200)
                    .IsRequired();

                // Check constraints (will be added via SQL scripts)
                entity.ToTable(tb => tb.HasCheckConstraint(
                    "CK_ClientApplications_ApplicationType",
                    "ApplicationType IN ('Desktop', 'Web', 'Mobile', 'Service')"
                ));

                // Indexes
                entity.HasIndex(e => e.CreatedBy);
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.CreatedAt);
            });

            // Configure PendingClientRegistration
            modelBuilder.Entity<PendingClientRegistration>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.ClientName)
                    .HasMaxLength(200)
                    .IsRequired();

                entity.Property(e => e.ApplicationType)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.RequestedBy)
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.BusinessJustification)
                    .HasMaxLength(1000)
                    .IsRequired();

                entity.Property(e => e.ExpectedMonthlyVolume)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.ContactEmail)
                    .HasMaxLength(200)
                    .IsRequired();

                entity.Property(e => e.Status)
                    .HasMaxLength(20)
                    .HasDefaultValue("Pending");

                // Check constraints
                entity.ToTable(tb => tb.HasCheckConstraint(
                    "CK_PendingRegistrations_ApplicationType",
                    "ApplicationType IN ('Desktop', 'Web', 'Mobile', 'Service')"
                ));

                entity.ToTable(tb => tb.HasCheckConstraint(
                    "CK_PendingRegistrations_Status",
                    "Status IN ('Pending', 'Approved', 'Rejected')"
                ));

                entity.ToTable(tb => tb.HasCheckConstraint(
                    "CK_PendingRegistrations_ExpectedVolume",
                    "ExpectedMonthlyVolume IN ('Low', 'Medium', 'High', 'Enterprise')"
                ));

                // Indexes
                entity.HasIndex(e => e.RequestedBy);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.RequestedAt);

                // Foreign key relationship
                entity.HasOne(e => e.ApprovedClient)
                    .WithMany()
                    .HasForeignKey(e => e.ApprovedClientId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure RegisteredAppInstance
            modelBuilder.Entity<RegisteredAppInstance>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.ClientId)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.AppGuid)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.AppName)
                    .HasMaxLength(200)
                    .IsRequired();

                // Unique constraint for ClientId + AppGuid
                entity.HasIndex(e => new { e.ClientId, e.AppGuid })
                    .IsUnique()
                    .HasDatabaseName("UK_RegisteredAppInstances_ClientApp");

                // Indexes
                entity.HasIndex(e => e.ClientId);
                entity.HasIndex(e => e.IsActive);
                entity.HasIndex(e => e.FirstRegisteredAt);

                // Foreign key relationship
                entity.HasOne(e => e.ClientApplication)
                    .WithMany(c => c.RegisteredAppInstances)
                    .HasForeignKey(e => e.ClientId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure TelemetryUsage
            modelBuilder.Entity<TelemetryUsage>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.ClientId)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.EventType)
                    .HasMaxLength(50)
                    .IsRequired();

                entity.Property(e => e.UsageDate)
                    .HasColumnType("date");

                // Unique constraint for daily aggregation
                entity.HasIndex(e => new { e.ClientId, e.AppGuid, e.EventType, e.UsageDate })
                    .IsUnique()
                    .HasDatabaseName("UK_TelemetryUsage_Daily");

                // Indexes
                entity.HasIndex(e => e.ClientId);
                entity.HasIndex(e => e.UsageDate);
                entity.HasIndex(e => e.EventType);

                // Foreign key relationship
                entity.HasOne(e => e.ClientApplication)
                    .WithMany(c => c.TelemetryUsageRecords)
                    .HasForeignKey(e => e.ClientId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure ClientAuditLog
            modelBuilder.Entity<ClientAuditLog>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Action)
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.PerformedBy)
                    .HasMaxLength(100)
                    .IsRequired();

                entity.Property(e => e.Details)
                    .HasColumnType("nvarchar(max)");

                // Indexes
                entity.HasIndex(e => e.ClientId)
                    .HasDatabaseName("IX_ClientAuditLog_ClientId");

                entity.HasIndex(e => e.PerformedAt)
                    .IsDescending()
                    .HasDatabaseName("IX_ClientAuditLog_PerformedAt");

                entity.HasIndex(e => e.PerformedBy);
                entity.HasIndex(e => e.Action);

                // Foreign key relationship
                entity.HasOne(e => e.ClientApplication)
                    .WithMany(c => c.AuditLogEntries)
                    .HasForeignKey(e => e.ClientId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }
    }
}
