using BecaTracktion.Data;
using BecaTracktion.Data.Entities;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

namespace BecaTracktion.Services
{
    /// <summary>
    /// Service to handle data migration from in-memory to database storage
    /// </summary>
    public class DataMigrationService
    {
        private readonly BecaTracktionDbContext _context;
        private readonly ILogger<DataMigrationService> _logger;

        public DataMigrationService(BecaTracktionDbContext context, ILogger<DataMigrationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Migrate default clients from in-memory to database if they don't exist
        /// This ensures backward compatibility during the transition
        /// </summary>
        public async Task MigrateDefaultClientsAsync()
        {
            try
            {
                _logger.LogInformation("Starting default client migration...");

                // Define the default clients (same as in InMemoryAppVerificationService)
                var defaultClients = new[]
                {
                    new
                    {
                        ClientId = "revit-addin-client",
                        ClientSecret = "revit-secret-2024",
                        ClientName = "Revit Add-in Client",
                        ApplicationType = "Desktop",
                        Description = "Default client for Revit add-in applications"
                    },
                    new
                    {
                        ClientId = "python-script-client",
                        ClientSecret = "python-secret-2024",
                        ClientName = "Python Script Client",
                        ApplicationType = "Service",
                        Description = "Default client for Python automation scripts"
                    },
                    new
                    {
                        ClientId = "test-client",
                        ClientSecret = "test-secret-2024",
                        ClientName = "Test Client",
                        ApplicationType = "Service",
                        Description = "Default client for testing and development"
                    }
                };

                int migratedCount = 0;

                foreach (var defaultClient in defaultClients)
                {
                    // Check if client already exists
                    var existingClient = await _context.ClientApplications
                        .FirstOrDefaultAsync(c => c.ClientId == defaultClient.ClientId);

                    if (existingClient == null)
                    {
                        // Create the client
                        var newClient = new ClientApplication
                        {
                            ClientId = defaultClient.ClientId,
                            ClientName = defaultClient.ClientName,
                            ClientDescription = defaultClient.Description,
                            ApplicationType = defaultClient.ApplicationType,
                            ClientSecret = defaultClient.ClientSecret,
                            ClientSecretHash = HashSecret(defaultClient.ClientSecret),
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = "<EMAIL>",
                            ApprovedAt = DateTime.UtcNow,
                            ApprovedBy = "<EMAIL>",
                            ContactEmail = "<EMAIL>",
                            BusinessJustification = "Default client migrated from in-memory storage",
                            ExpectedMonthlyVolume = "Medium",
                            AllowedRoles = "[\"telemetry_client\"]"
                        };

                        _context.ClientApplications.Add(newClient);
                        migratedCount++;

                        _logger.LogInformation("Migrated default client: {ClientId}", defaultClient.ClientId);
                    }
                    else
                    {
                        // Update the hash if it's still a placeholder
                        if (existingClient.ClientSecretHash.StartsWith("HASH_PLACEHOLDER_"))
                        {
                            existingClient.ClientSecretHash = HashSecret(defaultClient.ClientSecret);
                            _logger.LogInformation("Updated hash for existing client: {ClientId}", defaultClient.ClientId);
                        }
                    }
                }

                if (migratedCount > 0)
                {
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("Successfully migrated {Count} default clients to database", migratedCount);

                    // Log the migration event
                    var auditEntry = new ClientAuditLog
                    {
                        Action = "Default Clients Migration",
                        Details = $"{{\"migratedCount\": {migratedCount}, \"timestamp\": \"{DateTime.UtcNow:O}\"}}",
                        PerformedBy = "<EMAIL>",
                        PerformedAt = DateTime.UtcNow
                    };

                    _context.ClientAuditLog.Add(auditEntry);
                    await _context.SaveChangesAsync();
                }
                else
                {
                    _logger.LogInformation("No default clients needed migration - all already exist in database");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during default client migration");
                throw;
            }
        }

        /// <summary>
        /// Verify that all required database objects exist
        /// </summary>
        public async Task<bool> VerifyDatabaseSchemaAsync()
        {
            try
            {
                _logger.LogInformation("Verifying database schema...");

                // Check if all required tables exist
                var tableNames = new[] 
                { 
                    "ClientApplications", 
                    "PendingClientRegistrations", 
                    "RegisteredAppInstances", 
                    "TelemetryUsage", 
                    "ClientAuditLog" 
                };

                foreach (var tableName in tableNames)
                {
                    var tableExists = await _context.Database
                        .SqlQueryRaw<int>($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}'")
                        .FirstOrDefaultAsync();

                    if (tableExists == 0)
                    {
                        _logger.LogError("Required table {TableName} does not exist", tableName);
                        return false;
                    }
                }

                // Check if views exist
                var viewNames = new[] 
                { 
                    "vw_ClientManagement", 
                    "vw_PendingRegistrations", 
                    "vw_UsageAnalytics", 
                    "vw_AppInstancesSummary", 
                    "vw_RecentAuditLog" 
                };

                foreach (var viewName in viewNames)
                {
                    var viewExists = await _context.Database
                        .SqlQueryRaw<int>($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = '{viewName}'")
                        .FirstOrDefaultAsync();

                    if (viewExists == 0)
                    {
                        _logger.LogWarning("PowerApps view {ViewName} does not exist - PowerApps functionality may be limited", viewName);
                    }
                }

                // Check if stored procedures exist
                var procedureNames = new[] 
                { 
                    "sp_GenerateClientCredentials", 
                    "sp_SubmitClientRegistration", 
                    "sp_ApproveClientRegistration", 
                    "sp_RejectClientRegistration" 
                };

                foreach (var procedureName in procedureNames)
                {
                    var procedureExists = await _context.Database
                        .SqlQueryRaw<int>($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = '{procedureName}' AND ROUTINE_TYPE = 'PROCEDURE'")
                        .FirstOrDefaultAsync();

                    if (procedureExists == 0)
                    {
                        _logger.LogWarning("PowerApps procedure {ProcedureName} does not exist - PowerApps functionality may be limited", procedureName);
                    }
                }

                _logger.LogInformation("Database schema verification completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during database schema verification");
                return false;
            }
        }

        /// <summary>
        /// Get migration status and statistics
        /// </summary>
        public async Task<object> GetMigrationStatusAsync()
        {
            try
            {
                var clientCount = await _context.ClientApplications.CountAsync();
                var pendingCount = await _context.PendingClientRegistrations.CountAsync(p => p.Status == "Pending");
                var appInstanceCount = await _context.RegisteredAppInstances.CountAsync();
                var auditLogCount = await _context.ClientAuditLog.CountAsync();

                var defaultClients = await _context.ClientApplications
                    .Where(c => c.CreatedBy == "<EMAIL>")
                    .Select(c => new { c.ClientId, c.ClientName, c.IsActive })
                    .ToListAsync();

                return new
                {
                    DatabaseConnected = true,
                    TotalClients = clientCount,
                    PendingRegistrations = pendingCount,
                    AppInstances = appInstanceCount,
                    AuditLogEntries = auditLogCount,
                    DefaultClients = defaultClients,
                    MigrationComplete = clientCount > 0,
                    LastChecked = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration status");
                return new
                {
                    DatabaseConnected = false,
                    Error = ex.Message,
                    LastChecked = DateTime.UtcNow
                };
            }
        }

        /// <summary>
        /// Hash a client secret using SHA256
        /// </summary>
        private static string HashSecret(string secret)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(secret));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
