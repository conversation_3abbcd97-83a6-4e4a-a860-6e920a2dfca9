using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BecaTracktion.Data.Entities
{
    /// <summary>
    /// Entity representing audit log entries for client operations
    /// </summary>
    [Table("ClientAuditLog")]
    public class ClientAuditLog
    {
        /// <summary>
        /// Unique identifier for this audit log entry
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        /// <summary>
        /// Client ID associated with this action (optional)
        /// </summary>
        [StringLength(50)]
        public string? ClientId { get; set; }

        /// <summary>
        /// Action that was performed
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Detailed information about the action (JSON format)
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? Details { get; set; }

        /// <summary>
        /// Employee who performed the action
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PerformedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the action was performed
        /// </summary>
        public DateTime PerformedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// IP address of the user who performed the action
        /// </summary>
        [StringLength(45)]
        public string? IPAddress { get; set; }

        /// <summary>
        /// User agent information
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }

        // Navigation property
        /// <summary>
        /// The client application associated with this audit entry
        /// </summary>
        [ForeignKey(nameof(ClientId))]
        public virtual ClientApplication? ClientApplication { get; set; }
    }
}
