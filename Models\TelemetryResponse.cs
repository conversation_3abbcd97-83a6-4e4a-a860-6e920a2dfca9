namespace BecaTracktion.Models
{
    /// <summary>
    /// Response returned after telemetry submission
    /// </summary>
    public class TelemetryResponse
    {
        /// <summary>
        /// Indicates if the telemetry was successfully received
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Message describing the result
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the telemetry was processed (UTC)
        /// </summary>
        public DateTime ProcessedAt { get; set; }

        /// <summary>
        /// Optional error details if submission failed
        /// </summary>
        public string? ErrorDetails { get; set; }
    }
}

