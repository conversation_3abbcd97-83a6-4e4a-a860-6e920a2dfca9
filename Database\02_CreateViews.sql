-- =============================================
-- PowerApps Optimized Views
-- These views make it easier for PowerApps to display and filter data
-- =============================================

-- =============================================
-- Client Management View
-- Main view for PowerApps client management interface
-- =============================================
CREATE VIEW vw_ClientManagement AS
SELECT 
    ca.ClientId,
    ca.ClientName,
    ca.ClientDescription,
    ca.ApplicationType,
    ca.CreatedBy,
    ca.CreatedAt,
    ca.ApprovedBy,
    ca.ApprovedAt,
    ca.IsActive,
    ca.ExpiresAt,
    ca.ContactEmail,
    ca.BusinessJustification,
    ca.ExpectedMonthlyVolume,
    ca.LastUsedAt,
    
    -- Computed columns for PowerApps
    CASE 
        WHEN ca.ExpiresAt IS NULL THEN 'No Expiration'
        WHEN ca.ExpiresAt > GETUTCDATE() THEN 'Active'
        ELSE 'Expired'
    END AS ExpirationStatus,
    
    CASE 
        WHEN ca.IsActive = 1 AND (ca.ExpiresAt IS NULL OR ca.ExpiresAt > GETUTCDATE()) THEN 'Active'
        WHEN ca.IsActive = 0 THEN 'Disabled'
        ELSE 'Expired'
    END AS OverallStatus,
    
    DATEDIFF(DAY, ca.CreatedAt, GETUTCDATE()) AS DaysSinceCreated,
    DATEDIFF(DAY, ca.LastUsedAt, GETUTCDATE()) AS DaysSinceLastUsed,
    
    -- Count of registered app instances
    (SELECT COUNT(*) FROM RegisteredAppInstances rai WHERE rai.ClientId = ca.ClientId AND rai.IsActive = 1) AS ActiveAppInstances,
    
    -- Total tokens issued
    (SELECT ISNULL(SUM(rai.TokensIssued), 0) FROM RegisteredAppInstances rai WHERE rai.ClientId = ca.ClientId) AS TotalTokensIssued

FROM ClientApplications ca;

-- =============================================
-- Pending Registrations View
-- For admin approval interface in PowerApps
-- =============================================
CREATE VIEW vw_PendingRegistrations AS
SELECT 
    pr.Id,
    pr.ClientName,
    pr.ClientDescription,
    pr.ApplicationType,
    pr.RequestedBy,
    pr.RequestedAt,
    pr.BusinessJustification,
    pr.ExpectedMonthlyVolume,
    pr.ContactEmail,
    pr.Status,
    pr.ReviewedBy,
    pr.ReviewedAt,
    pr.ReviewComments,
    pr.ApprovedClientId,
    
    -- Computed columns
    DATEDIFF(DAY, pr.RequestedAt, GETUTCDATE()) AS DaysPending,
    
    CASE 
        WHEN pr.Status = 'Pending' AND DATEDIFF(DAY, pr.RequestedAt, GETUTCDATE()) > 7 THEN 'Overdue'
        WHEN pr.Status = 'Pending' THEN 'Pending Review'
        WHEN pr.Status = 'Approved' THEN 'Approved'
        WHEN pr.Status = 'Rejected' THEN 'Rejected'
        ELSE pr.Status
    END AS StatusDescription,
    
    -- Priority based on volume and age
    CASE 
        WHEN pr.ExpectedMonthlyVolume = 'Enterprise' THEN 1
        WHEN pr.ExpectedMonthlyVolume = 'High' THEN 2
        WHEN pr.ExpectedMonthlyVolume = 'Medium' THEN 3
        ELSE 4
    END AS VolumePriority

FROM PendingClientRegistrations pr
WHERE pr.Status = 'Pending' OR pr.ReviewedAt > DATEADD(DAY, -30, GETUTCDATE()); -- Show pending + recent reviews

-- =============================================
-- Usage Analytics View
-- For PowerApps reporting and analytics
-- =============================================
CREATE VIEW vw_UsageAnalytics AS
SELECT 
    tu.ClientId,
    ca.ClientName,
    ca.ApplicationType,
    tu.EventType,
    tu.UsageDate,
    tu.EventCount,
    
    -- Computed columns
    YEAR(tu.UsageDate) AS UsageYear,
    MONTH(tu.UsageDate) AS UsageMonth,
    DATENAME(MONTH, tu.UsageDate) AS UsageMonthName,
    DATEPART(WEEK, tu.UsageDate) AS UsageWeek,
    
    -- Running totals (for charts)
    SUM(tu.EventCount) OVER (
        PARTITION BY tu.ClientId, tu.EventType 
        ORDER BY tu.UsageDate 
        ROWS UNBOUNDED PRECEDING
    ) AS RunningTotal,
    
    -- Monthly totals
    SUM(tu.EventCount) OVER (
        PARTITION BY tu.ClientId, tu.EventType, YEAR(tu.UsageDate), MONTH(tu.UsageDate)
    ) AS MonthlyTotal

FROM TelemetryUsage tu
INNER JOIN ClientApplications ca ON tu.ClientId = ca.ClientId
WHERE tu.UsageDate >= DATEADD(MONTH, -12, GETUTCDATE()); -- Last 12 months

-- =============================================
-- App Instances Summary View
-- Shows all app instances with their status
-- =============================================
CREATE VIEW vw_AppInstancesSummary AS
SELECT 
    rai.Id,
    rai.ClientId,
    ca.ClientName,
    rai.AppGuid,
    rai.AppName,
    rai.AppVersion,
    rai.OperatingSystem,
    rai.FirstRegisteredAt,
    rai.LastUsedAt,
    rai.IsActive,
    rai.TokensIssued,
    
    -- Computed columns
    DATEDIFF(DAY, rai.FirstRegisteredAt, GETUTCDATE()) AS DaysSinceRegistration,
    DATEDIFF(DAY, rai.LastUsedAt, GETUTCDATE()) AS DaysSinceLastUsed,
    
    CASE 
        WHEN rai.IsActive = 0 THEN 'Inactive'
        WHEN rai.LastUsedAt IS NULL THEN 'Never Used'
        WHEN DATEDIFF(DAY, rai.LastUsedAt, GETUTCDATE()) > 30 THEN 'Dormant'
        WHEN DATEDIFF(DAY, rai.LastUsedAt, GETUTCDATE()) > 7 THEN 'Idle'
        ELSE 'Active'
    END AS ActivityStatus

FROM RegisteredAppInstances rai
INNER JOIN ClientApplications ca ON rai.ClientId = ca.ClientId;

-- =============================================
-- Audit Log Summary View
-- Recent audit activities for PowerApps dashboard
-- =============================================
CREATE VIEW vw_RecentAuditLog AS
SELECT 
    cal.Id,
    cal.ClientId,
    ca.ClientName,
    cal.Action,
    cal.PerformedBy,
    cal.PerformedAt,
    
    -- Computed columns
    CASE 
        WHEN cal.Action LIKE '%Created%' THEN 'Creation'
        WHEN cal.Action LIKE '%Updated%' OR cal.Action LIKE '%Modified%' THEN 'Modification'
        WHEN cal.Action LIKE '%Deleted%' OR cal.Action LIKE '%Deactivated%' THEN 'Deletion'
        WHEN cal.Action LIKE '%Approved%' OR cal.Action LIKE '%Rejected%' THEN 'Approval'
        WHEN cal.Action LIKE '%Token%' THEN 'Authentication'
        ELSE 'Other'
    END AS ActionCategory,
    
    DATEDIFF(MINUTE, cal.PerformedAt, GETUTCDATE()) AS MinutesAgo,
    
    CASE 
        WHEN DATEDIFF(MINUTE, cal.PerformedAt, GETUTCDATE()) < 60 THEN CAST(DATEDIFF(MINUTE, cal.PerformedAt, GETUTCDATE()) AS NVARCHAR) + ' minutes ago'
        WHEN DATEDIFF(HOUR, cal.PerformedAt, GETUTCDATE()) < 24 THEN CAST(DATEDIFF(HOUR, cal.PerformedAt, GETUTCDATE()) AS NVARCHAR) + ' hours ago'
        ELSE CAST(DATEDIFF(DAY, cal.PerformedAt, GETUTCDATE()) AS NVARCHAR) + ' days ago'
    END AS TimeAgoDescription

FROM ClientAuditLog cal
LEFT JOIN ClientApplications ca ON cal.ClientId = ca.ClientId
WHERE cal.PerformedAt >= DATEADD(DAY, -30, GETUTCDATE()) -- Last 30 days
;
