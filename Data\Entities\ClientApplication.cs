using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BecaTracktion.Data.Entities
{
    /// <summary>
    /// Entity representing an approved client application
    /// </summary>
    [Table("ClientApplications")]
    public class ClientApplication
    {
        /// <summary>
        /// Unique client identifier
        /// </summary>
        [Key]
        [StringLength(50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the client application
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// Description of the client application
        /// </summary>
        [StringLength(1000)]
        public string? ClientDescription { get; set; }

        /// <summary>
        /// Type of application (Desktop, Web, Mobile, Service)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// Plain text client secret (for PowerApps display only)
        /// </summary>
        [Required]
        [StringLength(256)]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// SHA256 hash of the client secret (for API validation)
        /// </summary>
        [Required]
        [StringLength(256)]
        public string ClientSecretHash { get; set; } = string.Empty;

        /// <summary>
        /// Whether the client is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// When the client was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Employee who requested the client (from SharePoint)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the client was approved
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Admin who approved the client
        /// </summary>
        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// When the client credentials expire (optional)
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Contact email for the client
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// Business justification for the client
        /// </summary>
        [StringLength(1000)]
        public string? BusinessJustification { get; set; }

        /// <summary>
        /// Expected monthly usage volume
        /// </summary>
        [StringLength(50)]
        public string? ExpectedMonthlyVolume { get; set; }

        /// <summary>
        /// Allowed roles for this client (JSON array)
        /// </summary>
        [StringLength(500)]
        public string AllowedRoles { get; set; } = "telemetry_client";

        /// <summary>
        /// Last time the client was used
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        // Navigation properties
        /// <summary>
        /// Registered app instances for this client
        /// </summary>
        public virtual ICollection<RegisteredAppInstance> RegisteredAppInstances { get; set; } = new List<RegisteredAppInstance>();

        /// <summary>
        /// Telemetry usage records for this client
        /// </summary>
        public virtual ICollection<TelemetryUsage> TelemetryUsageRecords { get; set; } = new List<TelemetryUsage>();

        /// <summary>
        /// Audit log entries for this client
        /// </summary>
        public virtual ICollection<ClientAuditLog> AuditLogEntries { get; set; } = new List<ClientAuditLog>();
    }
}
