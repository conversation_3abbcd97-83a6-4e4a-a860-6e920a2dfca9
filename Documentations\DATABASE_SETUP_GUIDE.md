# 🗄️ Database Setup Guide for Dummies

## 📋 Overview

This guide will help you set up the database for the BecaTracktion Client Registration System. The database stores client applications, pending registrations, app instances, usage analytics, and audit logs.

## 🎯 What You'll Accomplish

By following this guide, you will:
- ✅ Create all necessary database tables
- ✅ Set up PowerApps-optimized views
- ✅ Install stored procedures for complex operations
- ✅ Migrate existing default clients to the database
- ✅ Configure Entity Framework in the application

## 🛠️ Prerequisites

Before starting, make sure you have:
- [ ] Azure SQL Database created and accessible
- [ ] SQL Server Management Studio (SSMS) or Azure Data Studio installed
- [ ] Database connection string ready
- [ ] Admin access to the database

## 📊 Database Schema Overview

The system uses 5 main tables:

| Table | Purpose | Used By |
|-------|---------|---------|
| `ClientApplications` | Approved client credentials | API + PowerApps |
| `PendingClientRegistrations` | Registration requests awaiting approval | PowerApps |
| `RegisteredAppInstances` | Individual app instances per client | API |
| `TelemetryUsage` | Usage statistics and analytics | PowerApps |
| `ClientAuditLog` | Complete audit trail | PowerApps |

## 🚀 Step-by-Step Setup

### Step 1: Connect to Your Database

1. **Open SQL Server Management Studio (SSMS)**
2. **Connect to your Azure SQL Database**:
   - Server name: `your-server.database.windows.net`
   - Authentication: SQL Server Authentication
   - Login: Your database username
   - Password: Your database password

### Step 2: Create the Database Tables

1. **Open the SQL script**: `Database/01_CreateTables.sql`
2. **Copy the entire script**
3. **Paste it into a new query window in SSMS**
4. **Execute the script** (F5 or click Execute)

**Expected Result**: You should see messages like:
```
Command(s) completed successfully.
```

### Step 3: Create PowerApps Views

1. **Open the SQL script**: `Database/02_CreateViews.sql`
2. **Copy and execute the script** in SSMS

**What this does**: Creates optimized views that make it easier for PowerApps to display and filter data.

### Step 4: Create Stored Procedures

1. **Open the SQL script**: `Database/03_CreateStoredProcedures.sql`
2. **Copy and execute the script** in SSMS

**What this does**: Creates procedures for complex operations like client registration and approval.

### Step 5: Seed Default Clients

1. **Open the SQL script**: `Database/04_SeedDefaultClients.sql`
2. **Copy and execute the script** in SSMS

**What this does**: Migrates the existing hardcoded clients (revit-addin-client, python-script-client, test-client) to the database.

### Step 6: Verify the Setup

Run this query to verify everything was created correctly:

```sql
-- Check tables
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

-- Check views
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.VIEWS 
ORDER BY TABLE_NAME;

-- Check stored procedures
SELECT ROUTINE_NAME 
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ROUTINE_NAME;

-- Check default clients
SELECT ClientId, ClientName, ApplicationType, IsActive 
FROM ClientApplications;
```

**Expected Results**:
- **5 tables**: ClientApplications, ClientAuditLog, PendingClientRegistrations, RegisteredAppInstances, TelemetryUsage
- **5 views**: vw_AppInstancesSummary, vw_ClientManagement, vw_PendingRegistrations, vw_RecentAuditLog, vw_UsageAnalytics
- **3 procedures**: sp_ApproveClientRegistration, sp_GenerateClientCredentials, sp_RejectClientRegistration, sp_SubmitClientRegistration
- **3 default clients**: revit-addin-client, python-script-client, test-client

## ⚙️ Configure the Application

### Update Connection String

1. **Open `appsettings.json`** in your BecaTracktion project
2. **Update the connection string**:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=BecaTracktionDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true",
    "AzureSqlConnection": "Server=tcp:YOUR-SERVER.database.windows.net,1433;Initial Catalog=BecaTracktionDb;Persist Security Info=False;User ID=YOUR-USERNAME;Password=YOUR-PASSWORD;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  }
}
```

3. **Replace the placeholders**:
   - `YOUR-SERVER`: Your Azure SQL server name
   - `YOUR-USERNAME`: Your database username
   - `YOUR-PASSWORD`: Your database password

### Test the Connection

1. **Build the project**: `dotnet build`
2. **Run the application**: `dotnet run`
3. **Check the logs** for any database connection errors

## 🔍 Troubleshooting

### Common Issues and Solutions

#### ❌ "Cannot connect to database"
**Solution**: 
- Check your connection string
- Verify firewall rules allow your IP
- Ensure the database exists

#### ❌ "Object name 'ClientApplications' is invalid"
**Solution**: 
- Make sure you ran all the SQL scripts
- Check you're connected to the correct database

#### ❌ "JSON_OBJECT is not a recognized function"
**Solution**: 
- Your SQL Server version might not support JSON_OBJECT
- Replace JSON_OBJECT calls with manual JSON string construction

#### ❌ "Permission denied"
**Solution**: 
- Ensure your database user has CREATE TABLE permissions
- You might need to run scripts as database owner

## 📊 Understanding the Data Flow

### For PowerApps Registration:
1. **Developer** fills form in PowerApps
2. **PowerApps** calls `sp_SubmitClientRegistration`
3. **Data** goes into `PendingClientRegistrations` table
4. **Admin** reviews via `vw_PendingRegistrations` view
5. **Admin** approves via `sp_ApproveClientRegistration`
6. **Approved client** goes into `ClientApplications` table

### For API Authentication:
1. **Application** requests token from API
2. **API** validates credentials against `ClientApplications`
3. **API** logs usage in `TelemetryUsage`
4. **API** records actions in `ClientAuditLog`

## 🎉 Success Checklist

After completing this guide, you should have:
- [ ] All 5 database tables created
- [ ] All 5 PowerApps views created
- [ ] All 4 stored procedures created
- [ ] 3 default clients seeded
- [ ] Application connection string configured
- [ ] Application successfully connecting to database

## 📞 Next Steps

Once the database is set up:
1. **Test the existing API** to ensure it still works
2. **Implement SqlAppVerificationService** to use the database
3. **Create PowerApps application** to use the registration system
4. **Set up admin approval workflow** in PowerApps

## 💡 Pro Tips

- **Backup First**: Always backup your database before running scripts
- **Test Environment**: Set up a test database first
- **Monitor Performance**: Keep an eye on query performance as data grows
- **Regular Maintenance**: Plan for regular database maintenance and cleanup

---

**🎯 You're now ready to move on to implementing the SqlAppVerificationService!**
