namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents a registered application with its metadata
    /// </summary>
    public class RegisteredApp
    {
        /// <summary>
        /// Unique identifier for this registration
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Client ID associated with this app
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Application metadata
        /// </summary>
        public AppMetadata Metadata { get; set; } = new();

        /// <summary>
        /// When the app was first registered
        /// </summary>
        public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last time the app was used
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// Whether the app registration is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Number of tokens issued for this app
        /// </summary>
        public int TokensIssued { get; set; } = 0;
    }
}
