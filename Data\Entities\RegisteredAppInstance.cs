using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BecaTracktion.Data.Entities
{
    /// <summary>
    /// Entity representing a registered application instance for a client
    /// </summary>
    [Table("RegisteredAppInstances")]
    public class RegisteredAppInstance
    {
        /// <summary>
        /// Unique identifier for this app instance registration
        /// </summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Client ID this app instance belongs to
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Unique identifier for the application instance
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AppGuid { get; set; } = string.Empty;

        /// <summary>
        /// Name of the application
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AppName { get; set; } = string.Empty;

        /// <summary>
        /// Version of the application
        /// </summary>
        [StringLength(50)]
        public string? AppVersion { get; set; }

        /// <summary>
        /// Operating system information
        /// </summary>
        [StringLength(100)]
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// When the app instance was first registered
        /// </summary>
        public DateTime FirstRegisteredAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last time the app instance was used
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// Whether the app instance is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Number of tokens issued for this app instance
        /// </summary>
        public int TokensIssued { get; set; } = 0;

        /// <summary>
        /// Additional properties as JSON
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? AdditionalProperties { get; set; }

        // Navigation property
        /// <summary>
        /// The client application this instance belongs to
        /// </summary>
        [ForeignKey(nameof(ClientId))]
        public virtual ClientApplication ClientApplication { get; set; } = null!;
    }
}
