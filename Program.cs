using BecaTracktion.Services;
using BecaTracktion.Middleware;
using BecaTracktion.Data;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Configure Entity Framework
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
if (string.IsNullOrEmpty(connectionString))
{
    // Fallback to Azure SQL connection string if DefaultConnection is not available
    connectionString = builder.Configuration.GetConnectionString("AzureSqlConnection");
}

builder.Services.AddDbContext<BecaTracktionDbContext>(options =>
{
    options.UseSqlServer(connectionString);
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});

// Register telemetry service
builder.Services.AddSingleton<ITelemetryService, ApplicationInsightsTelemetryService>();

// Register authentication services
builder.Services.AddScoped<IAppVerificationService, SqlAppVerificationService>();
builder.Services.AddScoped<IJwtTokenService, JwtTokenService>();

// Register migration service
builder.Services.AddScoped<DataMigrationService>();

// Configure JWT Authentication
var jwtSecretKey = builder.Configuration["Jwt:SecretKey"] ?? "BecaTracktion-Super-Secret-Key-2024-Change-In-Production!";
var jwtIssuer = builder.Configuration["Jwt:Issuer"] ?? "BecaTracktion";
var jwtAudience = builder.Configuration["Jwt:Audience"] ?? "BecaTracktion-Clients";

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(jwtSecretKey)),
            ValidateIssuer = true,
            ValidIssuer = jwtIssuer,
            ValidateAudience = true,
            ValidAudience = jwtAudience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };
    });

builder.Services.AddAuthorization();

// Add CORS support for cross-platform clients
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "TRACKTION - Telemetry Microservice",
        Version = "v2",
        Description = "A lightweight, language-agnostic HTTP-based service designed to forward telemetry data to Azure Application Insights." +
        "The service supports multiple technologies out of the box, including .NET, JavaScript, Python, PowerShell, and Curl, allowing seamless integration across a wide range of platforms.",
        //Contact = new Microsoft.OpenApi.Models.OpenApiContact
        //{
        //    Name = "Beca",
        //    Email = "<EMAIL>"
        //}
    });

    // Add JWT Bearer Authentication to Swagger
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    // Include XML comments if available
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// Ensure database is created and migrated
using (var scope = app.Services.CreateScope())
{
    try
    {
        var context = scope.ServiceProvider.GetRequiredService<BecaTracktionDbContext>();
        var migrationService = scope.ServiceProvider.GetRequiredService<DataMigrationService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogInformation("Ensuring database is created...");
        await context.Database.EnsureCreatedAsync();

        logger.LogInformation("Verifying database schema...");
        var schemaValid = await migrationService.VerifyDatabaseSchemaAsync();

        if (schemaValid)
        {
            logger.LogInformation("Migrating default clients...");
            await migrationService.MigrateDefaultClientsAsync();

            var status = await migrationService.GetMigrationStatusAsync();
            logger.LogInformation("Migration status: {@Status}", status);
        }
        else
        {
            logger.LogWarning("Database schema verification failed. Some features may not work correctly.");
            logger.LogWarning("Please run the SQL scripts in the Database folder to set up the complete schema.");
        }
    }
    catch (Exception ex)
    {
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "Error during database initialization and migration");
        // Don't stop the application - it can still work with basic functionality
    }
}

// Configure the HTTP request pipeline.
// Enable Swagger in all environments (including Production) for API testing
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "BecaTracktion Telemetry API v2");
    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
});

app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

// Add token binding validation middleware
app.UseTokenBindingValidation();

app.MapControllers();

app.Run();
