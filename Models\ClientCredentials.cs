namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents client credentials for authentication
    /// </summary>
    public class ClientCredentials
    {
        /// <summary>
        /// Client identifier
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Client secret (hashed)
        /// </summary>
        public string ClientSecretHash { get; set; } = string.Empty;

        /// <summary>
        /// Client name/description
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the client is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// When the client was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last time the client was used
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// Allowed roles for this client
        /// </summary>
        public List<string> AllowedRoles { get; set; } = new() { "telemetry_client" };
    }
}
