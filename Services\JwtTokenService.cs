using BecaTracktion.Models;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;

namespace BecaTracktion.Services
{
    /// <summary>
    /// JWT token service implementation
    /// </summary>
    public class JwtTokenService : IJwtTokenService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<JwtTokenService> _logger;
        private readonly string _secretKey;
        private readonly string _issuer;
        private readonly string _audience;

        public JwtTokenService(IConfiguration configuration, ILogger<JwtTokenService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            _secretKey = _configuration["Jwt:SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");
            _issuer = _configuration["Jwt:Issuer"] ?? "BecaTracktion";
            _audience = _configuration["Jwt:Audience"] ?? "BecaTracktion-Clients";
        }

        public Task<string> GenerateTokenAsync(string clientId, AppMetadata appMetadata, List<string> roles, int expirationMinutes = 60)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_secretKey);
                
                var claims = new List<Claim>
                {
                    new(ClaimTypes.NameIdentifier, clientId),
                    new("client_id", clientId),
                    new("app_guid", appMetadata.AppGuid),
                    new("app_name", appMetadata.AppName),
                    new("app_version", appMetadata.AppVersion ?? ""),
                    new("operating_system", appMetadata.OperatingSystem ?? ""),
                    new("collected_at", appMetadata.CollectedAt.ToString("O")),
                    new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                    new(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
                };

                // Add roles
                foreach (var role in roles)
                {
                    claims.Add(new Claim(ClaimTypes.Role, role));
                }

                // Add additional properties if any
                if (appMetadata.AdditionalProperties != null)
                {
                    var additionalPropsJson = JsonSerializer.Serialize(appMetadata.AdditionalProperties);
                    claims.Add(new Claim("additional_properties", additionalPropsJson));
                }

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(claims),
                    Expires = DateTime.UtcNow.AddMinutes(expirationMinutes),
                    Issuer = _issuer,
                    Audience = _audience,
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
                };

                var token = tokenHandler.CreateToken(tokenDescriptor);
                var tokenString = tokenHandler.WriteToken(token);

                _logger.LogInformation("Generated JWT token for client: {ClientId}, App: {AppGuid}", clientId, appMetadata.AppGuid);
                return Task.FromResult(tokenString);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating JWT token for client: {ClientId}", clientId);
                throw;
            }
        }

        public Task<ClaimsPrincipal?> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(_secretKey);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _issuer,
                    ValidateAudience = true,
                    ValidAudience = _audience,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
                return Task.FromResult<ClaimsPrincipal?>(principal);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "JWT token validation failed");
                return Task.FromResult<ClaimsPrincipal?>(null);
            }
        }

        public AppMetadata? ExtractAppMetadataFromClaims(ClaimsPrincipal principal)
        {
            try
            {
                var appGuid = principal.FindFirst("app_guid")?.Value;
                var appName = principal.FindFirst("app_name")?.Value;

                if (string.IsNullOrEmpty(appGuid) || string.IsNullOrEmpty(appName))
                {
                    return null;
                }

                var metadata = new AppMetadata
                {
                    AppGuid = appGuid,
                    AppName = appName,
                    AppVersion = principal.FindFirst("app_version")?.Value,
                    OperatingSystem = principal.FindFirst("operating_system")?.Value
                };

                if (DateTime.TryParse(principal.FindFirst("collected_at")?.Value, out var collectedAt))
                {
                    metadata.CollectedAt = collectedAt;
                }

                // Extract additional properties if present
                var additionalPropsJson = principal.FindFirst("additional_properties")?.Value;
                if (!string.IsNullOrEmpty(additionalPropsJson))
                {
                    try
                    {
                        metadata.AdditionalProperties = JsonSerializer.Deserialize<Dictionary<string, string>>(additionalPropsJson);
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "Failed to deserialize additional properties from token");
                    }
                }

                return metadata;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting app metadata from claims");
                return null;
            }
        }

        public string? ExtractClientIdFromClaims(ClaimsPrincipal principal)
        {
            return principal.FindFirst("client_id")?.Value ?? principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }
    }
}
