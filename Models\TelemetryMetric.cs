namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents a metric telemetry event
    /// </summary>
    public class TelemetryMetric
    {
        /// <summary>
        /// Name of the metric (e.g., "ProcessingTime", "ItemsProcessed")
        /// </summary>
        public string MetricName { get; set; } = string.Empty;

        /// <summary>
        /// Value of the metric
        /// </summary>
        public double Value { get; set; }

        /// <summary>
        /// Custom properties associated with the metric
        /// </summary>
        public Dictionary<string, string>? Properties { get; set; }

        /// <summary>
        /// Optional user identifier
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// Optional session identifier
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// Timestamp of the metric (UTC)
        /// </summary>
        public DateTime? Timestamp { get; set; }
    }
}

