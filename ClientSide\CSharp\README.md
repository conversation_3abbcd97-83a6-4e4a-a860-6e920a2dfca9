# 📊 BecaTracktion C# Client Libraries

## 🎯 Overview

These C# client libraries provide easy integration with the BecaTracktion Telemetry API. They now include **JWT authentication** and **automatic app metadata collection** to work with the new client registration workflow.

## 📦 Available Clients

### 🔷 .NET Core/8.0 Client
- **File**: `TracktionClientNetCore/TracktionClient.cs`
- **Namespace**: `TracktionClientCore`
- **Target**: .NET 8.0+
- **JSON Library**: System.Text.Json

### 🔶 .NET Framework Client
- **File**: `TracktionClientNetFramework/TracktionClient.cs`
- **Namespace**: `TracktionClientFramework`
- **Target**: .NET Framework 4.8+
- **JSON Library**: Newtonsoft.Json

## 🚀 Getting Started

### Step 1: Register Your Application

Before using the client, you need to register your application through the PowerApps registration system:

1. **Access PowerApps Registration Form** (via company SharePoint)
2. **Fill Application Details**:
   - Application Name
   - Application Type (Desktop/Web/Mobile/Service)
   - Business Justification
   - Expected Monthly Volume
   - Contact Email
3. **Submit Registration** and wait for admin approval
4. **Receive ClientID and ClientSecret** via PowerApps interface

### Step 2: Install Dependencies

#### For .NET Core/8.0:
```xml
<!-- No additional dependencies required - uses built-in System.Text.Json -->
<PackageReference Include="System.Net.Http" Version="4.3.4" />
```

#### For .NET Framework 4.8:
```xml
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="System.Net.Http" Version="4.3.4" />
```

### Step 3: Initialize the Client

#### .NET Core Example:
```csharp
using TracktionClientCore;

// Initialize with your registered credentials
var client = new TracktionClient(
    baseUrl: "https://your-api.azurewebsites.net",
    clientId: "your-client-id-from-powerapps",
    clientSecret: "your-client-secret-from-powerapps",
    appName: "My Revit Add-in" // Optional custom name
);

// Test the connection
if (client.TestConnection())
{
    Console.WriteLine("✅ Connected successfully!");
}
else
{
    Console.WriteLine("❌ Connection failed!");
}
```

#### .NET Framework Example:
```csharp
using TracktionClientFramework;

// Initialize with your registered credentials
var client = new TracktionClient(
    baseUrl: "https://your-api.azurewebsites.net",
    clientId: "your-client-id-from-powerapps",
    clientSecret: "your-client-secret-from-powerapps",
    appName: "My Revit Add-in" // Optional custom name
);

// Test the connection
if (client.TestConnection())
{
    Console.WriteLine("✅ Connected successfully!");
}
else
{
    Console.WriteLine("❌ Connection failed!");
}
```

## 📊 Usage Examples

### Track Events
```csharp
// Simple event
client.TrackEvent("ButtonClicked");

// Event with properties
client.TrackEvent("CommandExecuted", new Dictionary<string, string>
{
    ["CommandName"] = "CreateWall",
    ["Category"] = "Modeling"
});

// Event with properties and metrics
client.TrackEvent("ProcessingCompleted", 
    properties: new Dictionary<string, string>
    {
        ["ProcessType"] = "ModelAnalysis",
        ["Status"] = "Success"
    },
    metrics: new Dictionary<string, double>
    {
        ["ProcessingTimeMs"] = 1250.5,
        ["ElementsProcessed"] = 150
    });
```

### Track Exceptions
```csharp
try
{
    // Your code that might throw an exception
    ProcessModel();
}
catch (Exception ex)
{
    // Track the exception
    client.TrackException(ex, "ProcessModel", new Dictionary<string, string>
    {
        ["ModelPath"] = @"C:\Projects\Sample.rvt",
        ["UserAction"] = "ModelProcessing"
    });
    
    // Re-throw or handle as needed
    throw;
}
```

### Track Metrics
```csharp
// Performance metrics
client.TrackMetric("LoadTime", 2.5, new Dictionary<string, string>
{
    ["FileSize"] = "25MB",
    ["FileType"] = "RVT"
});

// Usage metrics
client.TrackMetric("ElementCount", 1500);
```

### Track Page Views (for UI applications)
```csharp
// Track dialog or form views
client.TrackPageView("SettingsDialog", new Dictionary<string, string>
{
    ["DialogType"] = "Configuration",
    ["AccessMethod"] = "Menu"
});
```

### Flush Data
```csharp
// Ensure all telemetry is sent before closing
client.Flush();
```

## 🔧 Advanced Features

### Authentication Status
```csharp
// Check if currently authenticated
if (client.IsAuthenticated)
{
    Console.WriteLine("✅ Authenticated and ready");
}
else
{
    Console.WriteLine("⚠️ Authentication required");
}
```

### Connection Testing
```csharp
// Test connection and authentication
if (client.TestConnection())
{
    Console.WriteLine("✅ Connection and authentication successful");
}
else
{
    Console.WriteLine("❌ Connection or authentication failed");
}
```

### Automatic Features

The client automatically handles:
- **JWT Token Management**: Automatic token acquisition and refresh
- **App Metadata Collection**: Automatic collection of app name, version, OS info
- **Request Headers**: Automatic injection of required authentication and metadata headers
- **Error Handling**: Graceful handling of authentication and network errors

## 🏗️ Integration Examples

### Revit Add-in Integration
```csharp
public class MyRevitCommand : IExternalCommand
{
    private static TracktionClient _telemetryClient;
    
    static MyRevitCommand()
    {
        _telemetryClient = new TracktionClient(
            "https://your-api.azurewebsites.net",
            "your-client-id",
            "your-client-secret",
            "My Revit Add-in"
        );
    }
    
    public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
    {
        try
        {
            _telemetryClient.TrackEvent("CommandStarted", new Dictionary<string, string>
            {
                ["CommandName"] = "MyRevitCommand",
                ["RevitVersion"] = commandData.Application.Application.VersionNumber
            });
            
            // Your command logic here
            DoWork();
            
            _telemetryClient.TrackEvent("CommandCompleted");
            return Result.Succeeded;
        }
        catch (Exception ex)
        {
            _telemetryClient.TrackException(ex, "MyRevitCommand.Execute");
            message = ex.Message;
            return Result.Failed;
        }
    }
}
```

### WPF Application Integration
```csharp
public partial class MainWindow : Window
{
    private TracktionClient _telemetryClient;
    
    public MainWindow()
    {
        InitializeComponent();
        
        _telemetryClient = new TracktionClient(
            "https://your-api.azurewebsites.net",
            "your-client-id",
            "your-client-secret"
        );
        
        // Track application startup
        _telemetryClient.TrackEvent("ApplicationStarted");
    }
    
    private void Button_Click(object sender, RoutedEventArgs e)
    {
        _telemetryClient.TrackEvent("ButtonClicked", new Dictionary<string, string>
        {
            ["ButtonName"] = ((Button)sender).Name
        });
    }
    
    protected override void OnClosed(EventArgs e)
    {
        _telemetryClient.TrackEvent("ApplicationClosed");
        _telemetryClient.Flush();
        _telemetryClient.Dispose();
        base.OnClosed(e);
    }
}
```

## 🔒 Security Notes

- **Never hardcode credentials** in your source code
- **Store credentials securely** (app.config, environment variables, or secure storage)
- **Use different ClientID/Secret** for different environments (dev, staging, production)
- **Rotate secrets regularly** through the PowerApps admin interface

## 🐛 Troubleshooting

### Common Issues:

#### ❌ "Authentication failed: 401"
- **Cause**: Invalid ClientID or ClientSecret
- **Solution**: Verify credentials from PowerApps registration

#### ❌ "Connection timeout"
- **Cause**: Network connectivity or firewall issues
- **Solution**: Check network connection and firewall rules

#### ❌ "Client not found"
- **Cause**: ClientID not registered or deactivated
- **Solution**: Check registration status in PowerApps admin interface

#### ❌ "Token expired"
- **Cause**: Long-running application with expired token
- **Solution**: Client automatically handles token refresh - check network connectivity

## 📞 Support

For issues with:
- **Client Registration**: Contact your PowerApps administrator
- **API Issues**: Check the BecaTracktion API documentation
- **Client Library Issues**: Review this documentation and error messages

## 🎉 You're Ready!

Your C# application is now ready to send authenticated telemetry to BecaTracktion! The client handles all the complex authentication and metadata collection automatically.
