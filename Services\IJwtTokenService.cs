using BecaTracktion.Models;
using System.Security.Claims;

namespace BecaTracktion.Services
{
    /// <summary>
    /// Interface for JWT token operations
    /// </summary>
    public interface IJwtTokenService
    {
        /// <summary>
        /// Generate a JWT token for a client with embedded app metadata
        /// </summary>
        Task<string> GenerateTokenAsync(string clientId, AppMetadata appMetadata, List<string> roles, int expirationMinutes = 60);

        /// <summary>
        /// Validate a JWT token and extract claims
        /// </summary>
        Task<ClaimsPrincipal?> ValidateTokenAsync(string token);

        /// <summary>
        /// Extract app metadata from JWT claims
        /// </summary>
        AppMetadata? ExtractAppMetadataFromClaims(ClaimsPrincipal principal);

        /// <summary>
        /// Extract client ID from JWT claims
        /// </summary>
        string? ExtractClientIdFromClaims(ClaimsPrincipal principal);
    }
}
