using BecaTracktion.Models;
using BecaTracktion.Services;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;

namespace BecaTracktion.Controllers
{
    /// <summary>
    /// Authentication controller for JWT token issuance and management
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class AuthController : ControllerBase
    {
        private readonly IAppVerificationService _appVerificationService;
        private readonly IJwtTokenService _jwtTokenService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAppVerificationService appVerificationService,
            IJwtTokenService jwtTokenService,
            ILogger<AuthController> logger)
        {
            _appVerificationService = appVerificationService;
            _jwtTokenService = jwtTokenService;
            _logger = logger;
        }

        /// <summary>
        /// Issue a JWT token for authenticated clients with app verification
        /// </summary>
        /// <param name="request">Token request with client credentials and app metadata</param>
        /// <returns>JWT token response or error</returns>
        [HttpPost("token")]
        [ProducesResponseType(typeof(TokenResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(object), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(object), StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<TokenResponse>> IssueToken([FromBody] TokenRequest request)
        {
            try
            {
                // Validate request
                if (!ModelState.IsValid)
                {
                    return BadRequest(new
                    {
                        error = "invalid_request",
                        error_description = "Invalid request parameters",
                        details = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)
                    });
                }

                // Validate client credentials
                var isValidClient = await _appVerificationService.ValidateClientCredentialsAsync(
                    request.ClientId, request.ClientSecret);

                if (!isValidClient)
                {
                    _logger.LogWarning("Invalid client credentials: {ClientId}", request.ClientId);
                    return Unauthorized(new
                    {
                        error = "invalid_client",
                        error_description = "Invalid client credentials"
                    });
                }

                // Register or update app with metadata
                var (isFirstRegistration, registeredApp) = await _appVerificationService.RegisterOrUpdateAppAsync(
                    request.ClientId, request.AppMetadata);

                // Get client details for roles
                var clientCredentials = await _appVerificationService.GetClientCredentialsAsync(request.ClientId);
                if (clientCredentials == null)
                {
                    return Unauthorized(new
                    {
                        error = "invalid_client",
                        error_description = "Client not found"
                    });
                }

                // Generate JWT token with embedded app metadata
                var expirationMinutes = request.ExpirationMinutes ?? 60;
                var token = await _jwtTokenService.GenerateTokenAsync(
                    request.ClientId,
                    request.AppMetadata,
                    clientCredentials.AllowedRoles,
                    expirationMinutes);

                var response = new TokenResponse
                {
                    AccessToken = token,
                    TokenType = "Bearer",
                    ExpiresIn = expirationMinutes * 60,
                    IssuedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(expirationMinutes),
                    ClientId = request.ClientId,
                    IsFirstRegistration = isFirstRegistration
                };

                _logger.LogInformation("Issued JWT token for client: {ClientId}, App: {AppGuid}, First registration: {IsFirst}",
                    request.ClientId, request.AppMetadata.AppGuid, isFirstRegistration);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error issuing token for client: {ClientId}", request.ClientId);
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    error = "server_error",
                    error_description = "An error occurred while processing the token request"
                });
            }
        }

        /// <summary>
        /// Validate a JWT token (for testing purposes)
        /// </summary>
        /// <param name="token">JWT token to validate</param>
        /// <returns>Token validation result</returns>
        [HttpPost("validate")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
        public async Task<ActionResult> ValidateToken([FromBody] [Required] string token)
        {
            try
            {
                if (string.IsNullOrEmpty(token))
                {
                    return BadRequest(new
                    {
                        error = "invalid_request",
                        error_description = "Token is required"
                    });
                }

                var principal = await _jwtTokenService.ValidateTokenAsync(token);
                if (principal == null)
                {
                    return BadRequest(new
                    {
                        error = "invalid_token",
                        error_description = "Token is invalid or expired"
                    });
                }

                var clientId = _jwtTokenService.ExtractClientIdFromClaims(principal);
                var appMetadata = _jwtTokenService.ExtractAppMetadataFromClaims(principal);

                return Ok(new
                {
                    valid = true,
                    client_id = clientId,
                    app_metadata = appMetadata,
                    claims = principal.Claims.Select(c => new { c.Type, c.Value }).ToList()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    error = "server_error",
                    error_description = "An error occurred while validating the token"
                });
            }
        }

        /// <summary>
        /// Get information about registered clients (for development/testing)
        /// </summary>
        /// <returns>List of available client IDs</returns>
        [HttpGet("clients")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        public ActionResult GetClients()
        {
            // This endpoint provides information about available clients for development
            // In production, this should be secured or removed
            var clients = new[]
            {
                new { client_id = "revit-addin-client", description = "Revit Add-in Client" },
                new { client_id = "python-script-client", description = "Python Script Client" },
                new { client_id = "test-client", description = "Test Client" }
            };

            return Ok(new
            {
                message = "Available clients for development/testing",
                clients = clients,
                note = "Use the corresponding client secret to obtain tokens. This endpoint should be secured in production."
            });
        }
    }
}
