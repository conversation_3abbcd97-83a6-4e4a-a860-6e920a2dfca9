using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace TracktionClientCore
{
    /// <summary>
    /// Client for sending telemetry to BecaTracktion microservice
    /// This can be used in Revit add-ins 
    /// Compatible with .NET 8.0 
    /// </summary>
    public class TracktionClient : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _sessionId;

        public TracktionClient(string baseUrl, string userId = null)
        {
            _baseUrl = baseUrl.TrimEnd(new[] { '/' });
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(10)
            };
            _sessionId = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// Track a custom event (synchronous)
        /// </summary>
        public bool TrackEvent(
            string eventName,
            Dictionary<string, string> properties = null,
            Dictionary<string, double> metrics = null)
        {
            try
            {
                var telemetryEvent = new
                {
                    EventName = eventName,
                    Properties = properties ?? new Dictionary<string, string>(),
                    Metrics = metrics,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryEvent);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/event",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking event: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track an exception (synchronous)
        /// </summary>
        public bool TrackException(
            Exception exception,
            string location = null,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryException = new
                {
                    Message = exception.Message,
                    ExceptionType = exception.GetType().FullName,
                    StackTrace = exception.StackTrace,
                    Location = location,
                    Properties = properties ?? new Dictionary<string, string>(),
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryException);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/exception",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking exception: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a metric (synchronous)
        /// </summary>
        public bool TrackMetric(
            string metricName,
            double value,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryMetric = new
                {
                    MetricName = metricName,
                    Value = value,
                    Properties = properties,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryMetric);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/metric",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking metric: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a page view (synchronous)
        /// </summary>
        public bool TrackPageView(
            string pageName,
            Dictionary<string, string> properties = null)
        {
            try
            {
                var telemetryPageView = new
                {
                    PageName = pageName,
                    Properties = properties,
                    SessionId = _sessionId,
                    Timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryPageView);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/pageview",
                    content).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking page view: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Flush telemetry to ensure it's sent (synchronous)
        /// </summary>
        public bool Flush()
        {
            try
            {
                var response = _httpClient.PostAsync(
                    $"{_baseUrl}/api/telemetry/flush",
                    null).Result;

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error flushing telemetry: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
