using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace TracktionClientCore
{
    /// <summary>
    /// Authenticated client for sending telemetry to BecaTracktion microservice
    /// Now includes JWT authentication and automatic app metadata collection
    /// Compatible with .NET 8.0
    /// </summary>
    public class TracktionClient : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _sessionId;
        private readonly string _appGuid;
        private readonly string _appName;
        private readonly string _appVersion;
        private readonly string _operatingSystem;

        private string _accessToken;
        private DateTime _tokenExpiresAt;
        private readonly object _tokenLock = new object();

        /// <summary>
        /// Initialize TracktionClient with authentication credentials
        /// </summary>
        /// <param name="baseUrl">BecaTracktion API base URL</param>
        /// <param name="clientId">Client ID obtained from PowerApps registration</param>
        /// <param name="clientSecret">Client Secret obtained from PowerApps registration</param>
        /// <param name="appName">Optional custom app name (defaults to assembly name)</param>
        public TracktionClient(string baseUrl, string clientId, string clientSecret, string appName = null)
        {
            if (string.IsNullOrEmpty(baseUrl))
                throw new ArgumentException("Base URL cannot be null or empty", nameof(baseUrl));
            if (string.IsNullOrEmpty(clientId))
                throw new ArgumentException("Client ID cannot be null or empty", nameof(clientId));
            if (string.IsNullOrEmpty(clientSecret))
                throw new ArgumentException("Client Secret cannot be null or empty", nameof(clientSecret));

            _baseUrl = baseUrl.TrimEnd('/');
            _clientId = clientId;
            _clientSecret = clientSecret;
            _sessionId = Guid.NewGuid().ToString();

            // Collect app metadata automatically
            _appGuid = GetOrCreateAppGuid();
            _appName = appName ?? GetAppName();
            _appVersion = GetAppVersion();
            _operatingSystem = GetOperatingSystem();

            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromSeconds(30) // Increased timeout for authentication
            };
        }

        #region Authentication Methods

        /// <summary>
        /// Get a valid access token, refreshing if necessary
        /// </summary>
        private async Task<string> GetAccessTokenAsync()
        {
            lock (_tokenLock)
            {
                // Check if we have a valid token
                if (!string.IsNullOrEmpty(_accessToken) && DateTime.UtcNow < _tokenExpiresAt.AddMinutes(-5))
                {
                    return _accessToken;
                }
            }

            // Need to get a new token
            try
            {
                var tokenRequest = new
                {
                    clientId = _clientId,
                    clientSecret = _clientSecret,
                    appMetadata = new
                    {
                        appGuid = _appGuid,
                        appName = _appName,
                        appVersion = _appVersion,
                        operatingSystem = _operatingSystem,
                        collectedAt = DateTime.UtcNow.ToString("o")
                    }
                };

                var json = JsonSerializer.Serialize(tokenRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/token", content);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new InvalidOperationException($"Authentication failed: {response.StatusCode} - {errorContent}");
                }

                var responseJson = await response.Content.ReadAsStringAsync();
                var tokenResponse = JsonSerializer.Deserialize<JsonElement>(responseJson);

                lock (_tokenLock)
                {
                    _accessToken = tokenResponse.GetProperty("accessToken").GetString();
                    var expiresIn = tokenResponse.GetProperty("expiresIn").GetInt32();
                    _tokenExpiresAt = DateTime.UtcNow.AddSeconds(expiresIn);
                }

                return _accessToken;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to obtain access token: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get access token synchronously
        /// </summary>
        private string GetAccessToken()
        {
            return GetAccessTokenAsync().Result;
        }

        #endregion

        #region App Metadata Collection

        private string GetOrCreateAppGuid()
        {
            // Try to get a persistent app GUID (you might want to store this in registry or config)
            // For now, generate a new one each time (you can enhance this)
            return Guid.NewGuid().ToString();
        }

        private string GetAppName()
        {
            try
            {
                var assembly = Assembly.GetEntryAssembly() ?? Assembly.GetExecutingAssembly();
                return assembly.GetName().Name ?? "Unknown Application";
            }
            catch
            {
                return "Unknown Application";
            }
        }

        private string GetAppVersion()
        {
            try
            {
                var assembly = Assembly.GetEntryAssembly() ?? Assembly.GetExecutingAssembly();
                return assembly.GetName().Version?.ToString() ?? "*******";
            }
            catch
            {
                return "*******";
            }
        }

        private string GetOperatingSystem()
        {
            try
            {
                return $"{RuntimeInformation.OSDescription} ({RuntimeInformation.OSArchitecture})";
            }
            catch
            {
                return Environment.OSVersion.ToString();
            }
        }

        #endregion

        #region Telemetry Methods

        /// <summary>
        /// Track a custom event (synchronous)
        /// </summary>
        public bool TrackEvent(
            string eventName,
            Dictionary<string, string> properties = null,
            Dictionary<string, double> metrics = null)
        {
            try
            {
                // Get authentication token
                var token = GetAccessToken();

                var telemetryEvent = new
                {
                    name = eventName, // Updated to match API expectation
                    properties = properties ?? new Dictionary<string, string>(),
                    metrics = metrics,
                    sessionId = _sessionId,
                    timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryEvent);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add authentication and metadata headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/api/telemetry/event")
                {
                    Content = content
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                request.Headers.Add("X-App-Guid", _appGuid);
                request.Headers.Add("X-App-Name", _appName);

                var response = _httpClient.SendAsync(request).Result;
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking event: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track an exception (synchronous)
        /// </summary>
        public bool TrackException(
            Exception exception,
            string location = null,
            Dictionary<string, string> properties = null)
        {
            try
            {
                // Get authentication token
                var token = GetAccessToken();

                var telemetryException = new
                {
                    message = exception.Message,
                    exceptionType = exception.GetType().FullName,
                    stackTrace = exception.StackTrace,
                    location = location,
                    properties = properties ?? new Dictionary<string, string>(),
                    sessionId = _sessionId,
                    timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryException);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add authentication and metadata headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/api/telemetry/exception")
                {
                    Content = content
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                request.Headers.Add("X-App-Guid", _appGuid);
                request.Headers.Add("X-App-Name", _appName);

                var response = _httpClient.SendAsync(request).Result;
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking exception: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a metric (synchronous)
        /// </summary>
        public bool TrackMetric(
            string metricName,
            double value,
            Dictionary<string, string> properties = null)
        {
            try
            {
                // Get authentication token
                var token = GetAccessToken();

                var telemetryMetric = new
                {
                    name = metricName,
                    value = value,
                    properties = properties,
                    sessionId = _sessionId,
                    timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryMetric);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add authentication and metadata headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/api/telemetry/metric")
                {
                    Content = content
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                request.Headers.Add("X-App-Guid", _appGuid);
                request.Headers.Add("X-App-Name", _appName);

                var response = _httpClient.SendAsync(request).Result;
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking metric: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Track a page view (synchronous)
        /// </summary>
        public bool TrackPageView(
            string pageName,
            Dictionary<string, string> properties = null)
        {
            try
            {
                // Get authentication token
                var token = GetAccessToken();

                var telemetryPageView = new
                {
                    name = pageName,
                    properties = properties,
                    sessionId = _sessionId,
                    timestamp = DateTime.UtcNow
                };

                var json = JsonSerializer.Serialize(telemetryPageView);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // Add authentication and metadata headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/api/telemetry/pageview")
                {
                    Content = content
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                request.Headers.Add("X-App-Guid", _appGuid);
                request.Headers.Add("X-App-Name", _appName);

                var response = _httpClient.SendAsync(request).Result;
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error tracking page view: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Flush telemetry to ensure it's sent (synchronous)
        /// </summary>
        public bool Flush()
        {
            try
            {
                // Get authentication token
                var token = GetAccessToken();

                // Add authentication headers
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/api/telemetry/flush");
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
                request.Headers.Add("X-App-Guid", _appGuid);
                request.Headers.Add("X-App-Name", _appName);

                var response = _httpClient.SendAsync(request).Result;
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error flushing telemetry: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Test the connection and authentication
        /// </summary>
        public bool TestConnection()
        {
            try
            {
                var token = GetAccessToken();
                return !string.IsNullOrEmpty(token);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Connection test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get current authentication status
        /// </summary>
        public bool IsAuthenticated
        {
            get
            {
                lock (_tokenLock)
                {
                    return !string.IsNullOrEmpty(_accessToken) && DateTime.UtcNow < _tokenExpiresAt;
                }
            }
        }

        #endregion

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
