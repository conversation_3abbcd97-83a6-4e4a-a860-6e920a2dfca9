using BecaTracktion.Models;

namespace BecaTracktion.Services
{
    /// <summary>
    /// Interface for app verification and registration services
    /// </summary>
    public interface IAppVerificationService
    {
        /// <summary>
        /// Verify client credentials
        /// </summary>
        Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret);

        /// <summary>
        /// Register or update an app with its metadata
        /// </summary>
        Task<(bool IsFirstRegistration, RegisteredApp RegisteredApp)> RegisterOrUpdateAppAsync(string clientId, AppMetadata metadata);

        /// <summary>
        /// Validate that a token's metadata matches the current request context
        /// </summary>
        Task<bool> ValidateTokenBindingAsync(string clientId, AppMetadata tokenMetadata, AppMetadata currentMetadata);

        /// <summary>
        /// Get registered app by client ID and app GUID
        /// </summary>
        Task<RegisteredApp?> GetRegisteredAppAsync(string clientId, string appGuid);

        /// <summary>
        /// Update last used timestamp for an app
        /// </summary>
        Task UpdateLastUsedAsync(string clientId, string appGuid);

        /// <summary>
        /// Get client credentials by client ID
        /// </summary>
        Task<ClientCredentials?> GetClientCredentialsAsync(string clientId);
    }
}
