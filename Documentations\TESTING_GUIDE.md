# 🧪 Testing Guide for Client Registration Workflow

## 🎯 Overview

This guide provides comprehensive testing procedures to verify that the Client Registration Workflow is working correctly. Follow these tests in order to ensure everything is functioning properly.

## 📋 Pre-Testing Checklist

Before starting tests, ensure:
- [ ] Database schema is deployed (all SQL scripts executed)
- [ ] Application is running with SqlAppVerificationService
- [ ] Database connection is working
- [ ] PowerApps connection to database is established

## 🗄️ Database Testing

### Test 1: Verify Database Schema

**Purpose**: Ensure all database objects were created correctly

**Steps**:
1. Connect to your database using SSMS
2. Run this verification query:

```sql
-- Check tables
SELECT 'Tables' as ObjectType, TABLE_NAME as Name 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
UNION ALL
-- Check views
SELECT 'Views' as ObjectType, TABLE_NAME as Name 
FROM INFORMATION_SCHEMA.VIEWS 
UNION ALL
-- Check procedures
SELECT 'Procedures' as ObjectType, ROUTINE_NAME as Name 
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ObjectType, Name;
```

**Expected Results**:
- 5 Tables: ClientApplications, ClientAuditLog, PendingClientRegistrations, RegisteredAppInstances, TelemetryUsage
- 5 Views: vw_AppInstancesSummary, vw_ClientManagement, vw_PendingRegistrations, vw_RecentAuditLog, vw_UsageAnalytics
- 4 Procedures: sp_ApproveClientRegistration, sp_GenerateClientCredentials, sp_RejectClientRegistration, sp_SubmitClientRegistration

### Test 2: Verify Default Clients Migration

**Purpose**: Ensure default clients were migrated correctly

**Steps**:
```sql
SELECT ClientId, ClientName, ApplicationType, IsActive, CreatedBy
FROM ClientApplications
WHERE CreatedBy = '<EMAIL>';
```

**Expected Results**:
- 3 clients: revit-addin-client, python-script-client, test-client
- All should be active (IsActive = 1)
- CreatedBy should be '<EMAIL>'

### Test 3: Test Stored Procedures

**Purpose**: Verify stored procedures work correctly

**Steps**:
```sql
-- Test client registration submission
DECLARE @RegistrationId UNIQUEIDENTIFIER, @Result NVARCHAR(100);
EXEC sp_SubmitClientRegistration 
    @ClientName = 'Test Application',
    @ClientDescription = 'Test application for verification',
    @ApplicationType = 'Desktop',
    @RequestedBy = '<EMAIL>',
    @BusinessJustification = 'Testing the registration workflow',
    @ExpectedMonthlyVolume = 'Low',
    @ContactEmail = '<EMAIL>',
    @RegistrationId = @RegistrationId OUTPUT,
    @Result = @Result OUTPUT;

SELECT @RegistrationId as RegistrationId, @Result as Result;

-- Verify the registration was created
SELECT * FROM PendingClientRegistrations WHERE Id = @RegistrationId;
```

**Expected Results**:
- @Result should be 'SUCCESS: Registration request submitted successfully'
- Registration should appear in PendingClientRegistrations table

## 🔧 Application Testing

### Test 4: Application Startup

**Purpose**: Verify application starts correctly with database integration

**Steps**:
1. Open terminal in project directory
2. Run: `dotnet run`
3. Check the startup logs

**Expected Log Messages**:
```
info: Program[0] Ensuring database is created...
info: Program[0] Verifying database schema...
info: BecaTracktion.Services.DataMigrationService[0] Database schema verification completed successfully
info: Program[0] Migrating default clients...
info: BecaTracktion.Services.DataMigrationService[0] No default clients needed migration - all already exist in database
```

### Test 5: API Authentication

**Purpose**: Verify existing API authentication still works

**Steps**:
1. Use PowerShell or curl to test token request:

```powershell
# PowerShell test
$body = @{
    clientId = "test-client"
    clientSecret = "test-secret-2024"
    appMetadata = @{
        appGuid = [System.Guid]::NewGuid().ToString()
        appName = "Test Application"
        appVersion = "1.0.0"
        operatingSystem = "Windows 10"
        collectedAt = (Get-Date).ToString("o")
    }
} | ConvertTo-Json -Depth 3

$response = Invoke-RestMethod -Uri "https://localhost:5001/api/auth/token" -Method POST -Body $body -ContentType "application/json"
Write-Host "Token received: $($response.accessToken.Substring(0,20))..."
```

**Expected Results**:
- Should receive a JWT token
- No errors in application logs
- Token should be valid for subsequent API calls

### Test 6: Telemetry Endpoints

**Purpose**: Verify telemetry endpoints work with database authentication

**Steps**:
1. Use the token from Test 5 to send telemetry:

```powershell
# Using token from previous test
$headers = @{
    "Authorization" = "Bearer $($response.accessToken)"
    "X-App-Guid" = $appGuid
    "X-App-Name" = "Test Application"
}

$eventBody = @{
    name = "TestEvent"
    properties = @{
        testProperty = "testValue"
    }
} | ConvertTo-Json

$eventResponse = Invoke-RestMethod -Uri "https://localhost:5001/api/telemetry/event" -Method POST -Body $eventBody -ContentType "application/json" -Headers $headers
Write-Host "Event sent successfully: $($eventResponse.success)"
```

**Expected Results**:
- Event should be sent successfully
- Check database for new entries in RegisteredAppInstances table

## 📱 PowerApps Testing

### Test 7: Database Connection

**Purpose**: Verify PowerApps can connect to database

**Steps**:
1. Open PowerApps Studio
2. Create new Canvas App
3. Add SQL Server data source
4. Try to browse the tables and views

**Expected Results**:
- Connection should be successful
- All tables and views should be visible
- Sample data should load correctly

### Test 8: Registration Form Test

**Purpose**: Test the registration workflow end-to-end

**Steps**:
1. Create a simple PowerApps form with these fields:
   - ClientName (Text Input)
   - ApplicationType (Dropdown: Desktop, Web, Mobile, Service)
   - RequestedBy (Text Input - use your email)
   - BusinessJustification (Text Input)
   - ExpectedMonthlyVolume (Dropdown: Low, Medium, High, Enterprise)
   - ContactEmail (Text Input)

2. Add submit button with this formula:
```powerquery
Patch(
    PendingClientRegistrations,
    Defaults(PendingClientRegistrations),
    {
        ClientName: TextInput_AppName.Text,
        ApplicationType: Dropdown_AppType.Selected.Value,
        RequestedBy: User().Email,
        BusinessJustification: TextInput_Justification.Text,
        ExpectedMonthlyVolume: Dropdown_Volume.Selected.Value,
        ContactEmail: TextInput_ContactEmail.Text,
        Status: "Pending"
    }
)
```

3. Submit a test registration

**Expected Results**:
- Form should submit without errors
- New record should appear in PendingClientRegistrations table
- Status should be "Pending"

### Test 9: Admin Approval Test

**Purpose**: Test the approval workflow

**Steps**:
1. Create admin interface to view pending registrations
2. Add approve button that calls stored procedure:

```powerquery
With(
    {
        result: 'your-sql-connection'.ExecuteProcedure(
            "sp_ApproveClientRegistration",
            {
                RegistrationId: ThisItem.Id,
                ApprovedBy: User().Email,
                ReviewComments: "Approved for testing"
            }
        )
    },
    Notify(result.Result, NotificationType.Success)
)
```

3. Approve the test registration from Test 8

**Expected Results**:
- Approval should succeed
- New client should appear in ClientApplications table
- ClientId and ClientSecret should be generated
- Status should change to "Approved"

## 🔍 Data Verification Tests

### Test 10: Audit Logging

**Purpose**: Verify all operations are being logged

**Steps**:
```sql
-- Check recent audit log entries
SELECT TOP 10 
    Action,
    PerformedBy,
    PerformedAt,
    ClientId
FROM ClientAuditLog
ORDER BY PerformedAt DESC;
```

**Expected Results**:
- Should see entries for token generation, registration, approval
- All entries should have proper timestamps and user information

### Test 11: Usage Analytics

**Purpose**: Verify usage data is being collected

**Steps**:
1. Send several telemetry events using different event types
2. Check the analytics view:

```sql
SELECT * FROM vw_UsageAnalytics
WHERE UsageDate = CAST(GETUTCDATE() AS DATE);
```

**Expected Results**:
- Should see usage records for today
- Event counts should match the number of events sent

## 🚨 Error Testing

### Test 12: Invalid Credentials

**Purpose**: Verify error handling for invalid credentials

**Steps**:
```powershell
# Test with invalid client secret
$invalidBody = @{
    clientId = "test-client"
    clientSecret = "wrong-secret"
    appMetadata = @{
        appGuid = [System.Guid]::NewGuid().ToString()
        appName = "Test Application"
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "https://localhost:5001/api/auth/token" -Method POST -Body $invalidBody -ContentType "application/json"
} catch {
    Write-Host "Expected error: $($_.Exception.Message)"
}
```

**Expected Results**:
- Should receive 401 Unauthorized error
- Error should be logged in application logs

### Test 13: Rate Limiting

**Purpose**: Verify rate limiting works

**Steps**:
1. Submit 6 registration requests quickly from the same user
2. Check if the 6th request is rejected

**Expected Results**:
- First 5 requests should succeed
- 6th request should be rejected with rate limit error

## ✅ Success Criteria

All tests should pass with these results:

### Database Tests ✅
- [ ] All database objects created
- [ ] Default clients migrated
- [ ] Stored procedures working

### Application Tests ✅
- [ ] Application starts without errors
- [ ] Authentication working
- [ ] Telemetry endpoints functional

### PowerApps Tests ✅
- [ ] Database connection successful
- [ ] Registration form working
- [ ] Approval workflow functional

### Data Tests ✅
- [ ] Audit logging working
- [ ] Usage analytics collecting data

### Error Tests ✅
- [ ] Invalid credentials rejected
- [ ] Rate limiting enforced

## 🎉 Testing Complete!

If all tests pass, your Client Registration Workflow is ready for production use!

## 📞 Troubleshooting

If any tests fail, check:
1. **Database Connection**: Verify connection string is correct
2. **SQL Scripts**: Ensure all database scripts were executed
3. **Application Logs**: Check for detailed error messages
4. **PowerApps Permissions**: Verify PowerApps has database access
5. **Firewall Rules**: Ensure database firewall allows connections

For specific error messages, refer to the troubleshooting sections in the setup guides.
