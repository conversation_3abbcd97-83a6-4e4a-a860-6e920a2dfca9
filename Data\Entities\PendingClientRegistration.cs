using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BecaTracktion.Data.Entities
{
    /// <summary>
    /// Entity representing a pending client registration awaiting admin approval
    /// </summary>
    [Table("PendingClientRegistrations")]
    public class PendingClientRegistration
    {
        /// <summary>
        /// Unique identifier for this registration request
        /// </summary>
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Requested client name
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// Description of the client application
        /// </summary>
        [StringLength(1000)]
        public string? ClientDescription { get; set; }

        /// <summary>
        /// Type of application (Desktop, Web, Mobile, Service)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// Employee who requested the registration (from SharePoint)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RequestedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the registration was requested
        /// </summary>
        public DateTime RequestedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Business justification for the client
        /// </summary>
        [Required]
        [StringLength(1000)]
        public string BusinessJustification { get; set; } = string.Empty;

        /// <summary>
        /// Expected monthly usage volume
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ExpectedMonthlyVolume { get; set; } = string.Empty;

        /// <summary>
        /// Contact email for the client
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// Current status of the registration (Pending, Approved, Rejected)
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Admin who reviewed the registration
        /// </summary>
        [StringLength(100)]
        public string? ReviewedBy { get; set; }

        /// <summary>
        /// When the registration was reviewed
        /// </summary>
        public DateTime? ReviewedAt { get; set; }

        /// <summary>
        /// Comments from the reviewer
        /// </summary>
        [StringLength(1000)]
        public string? ReviewComments { get; set; }

        /// <summary>
        /// Client ID that was created when approved (links to ClientApplications)
        /// </summary>
        [StringLength(50)]
        public string? ApprovedClientId { get; set; }

        // Navigation property
        /// <summary>
        /// The approved client application (if approved)
        /// </summary>
        [ForeignKey(nameof(ApprovedClientId))]
        public virtual ClientApplication? ApprovedClient { get; set; }
    }
}
