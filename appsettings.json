{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=BecaTracktionDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "AzureSqlConnection": "Server=tcp:your-server.database.windows.net,1433;Initial Catalog=BecaTracktionDb;Persist Security Info=False;User ID=your-username;Password=your-password;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "ApplicationInsights": {"ConnectionString": "InstrumentationKey=23f5de61-4222-4e87-b594-13fb507f78d6;IngestionEndpoint=https://australiaeast-0.in.applicationinsights.azure.com/;LiveEndpoint=https://australiaeast.livediagnostics.monitor.azure.com/;ApplicationId=e54650bd-f014-4af4-a896-60158d105bf7"}, "Jwt": {"SecretKey": "BecaTracktion-Super-Secret-Key-2024-Change-In-Production!", "Issuer": "BecaTracktion", "Audience": "BecaTracktion-Clients", "ExpirationMinutes": 60}}