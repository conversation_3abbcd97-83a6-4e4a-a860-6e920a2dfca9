-- =============================================
-- BecaTracktion Client Registration Database Schema
-- PowerApps Integration Optimized
-- =============================================

-- Drop existing tables if they exist (for development)
IF OBJECT_ID('TelemetryUsage', 'U') IS NOT NULL DROP TABLE TelemetryUsage;
IF OBJECT_ID('ClientAuditLog', 'U') IS NOT NULL DROP TABLE ClientAuditLog;
IF OBJECT_ID('RegisteredAppInstances', 'U') IS NOT NULL DROP TABLE RegisteredAppInstances;
IF OBJECT_ID('PendingClientRegistrations', 'U') IS NOT NULL DROP TABLE PendingClientRegistrations;
IF OBJECT_ID('ClientApplications', 'U') IS NOT NULL DROP TABLE ClientApplications;

-- =============================================
-- Main Client Applications Table
-- This is where approved clients are stored
-- =============================================
CREATE TABLE ClientApplications (
    ClientId NVARCHAR(50) PRIMARY KEY DEFAULT NEWID(),
    ClientName NVARCHAR(200) NOT NULL,
    ClientDescription NVARCHAR(1000),
    ApplicationType NVARCHAR(50) NOT NULL, -- Desktop, Web, Mobile, Service
    ClientSecret NVARCHAR(256) NOT NULL, -- Plain text secret for PowerApps display
    ClientSecretHash NVARCHAR(256) NOT NULL, -- SHA256 hash for API validation
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(100) NOT NULL, -- Employee email from SharePoint
    ApprovedAt DATETIME2 NULL,
    ApprovedBy NVARCHAR(100) NULL, -- Admin who approved
    ExpiresAt DATETIME2 NULL, -- Admin can set expiration
    ContactEmail NVARCHAR(200) NOT NULL,
    BusinessJustification NVARCHAR(1000),
    --ExpectedMonthlyVolume NVARCHAR(50), -- Low, Medium, High, Enterprise
    AllowedRoles NVARCHAR(500) DEFAULT 'telemetry_client', -- JSON array of roles
    LastUsedAt DATETIME2 NULL,
    
    -- Constraints
    CONSTRAINT CK_ClientApplications_ApplicationType 
        CHECK (ApplicationType IN ('Desktop', 'Web', 'Mobile', 'Service')),
    --CONSTRAINT CK_ClientApplications_ExpectedVolume 
        --CHECK (ExpectedMonthlyVolume IN ('Low', 'Medium', 'High', 'Enterprise') OR ExpectedMonthlyVolume IS NULL)
);

-- =============================================
-- Pending Client Registrations Table
-- PowerApps writes here first, admin approves
-- =============================================
CREATE TABLE PendingClientRegistrations (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientName NVARCHAR(200) NOT NULL,
    ClientDescription NVARCHAR(1000),
    ApplicationType NVARCHAR(50) NOT NULL,
    RequestedBy NVARCHAR(100) NOT NULL, -- Employee email from SharePoint
    RequestedAt DATETIME2 DEFAULT GETUTCDATE(),
    BusinessJustification NVARCHAR(1000) NOT NULL,
    --ExpectedMonthlyVolume NVARCHAR(50) NOT NULL,
    ContactEmail NVARCHAR(200) NOT NULL,
    Status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Approved, Rejected
    ReviewedBy NVARCHAR(100) NULL, -- Admin who reviewed
    ReviewedAt DATETIME2 NULL,
    ReviewComments NVARCHAR(1000) NULL,
    ApprovedClientId NVARCHAR(50) NULL, -- Links to ClientApplications.ClientId when approved
    
    -- Constraints
    CONSTRAINT CK_PendingRegistrations_ApplicationType 
        CHECK (ApplicationType IN ('Desktop', 'Web', 'Mobile', 'Service')),
    CONSTRAINT CK_PendingRegistrations_Status 
        CHECK (Status IN ('Pending', 'Approved', 'Rejected')),
    --CONSTRAINT CK_PendingRegistrations_ExpectedVolume 
        --CHECK (ExpectedMonthlyVolume IN ('Low', 'Medium', 'High', 'Enterprise'))
);

-- =============================================
-- Registered App Instances Table
-- Tracks individual app instances per client
-- =============================================
CREATE TABLE RegisteredAppInstances (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId NVARCHAR(50) NOT NULL,
    AppGuid NVARCHAR(50) NOT NULL,
    AppName NVARCHAR(200) NOT NULL,
    AppVersion NVARCHAR(50),
    OperatingSystem NVARCHAR(100),
    FirstRegisteredAt DATETIME2 DEFAULT GETUTCDATE(),
    LastUsedAt DATETIME2 NULL,
    IsActive BIT DEFAULT 1,
    TokensIssued INT DEFAULT 0,
    AdditionalProperties NVARCHAR(MAX), -- JSON for extra metadata
    
    -- Foreign Key
    CONSTRAINT FK_RegisteredAppInstances_ClientApplications 
        FOREIGN KEY (ClientId) REFERENCES ClientApplications(ClientId) ON DELETE CASCADE,
    
    -- Unique constraint for client + app combination
    CONSTRAINT UK_RegisteredAppInstances_ClientApp 
        UNIQUE (ClientId, AppGuid)
);

-- =============================================
-- Telemetry Usage Analytics Table
-- Tracks usage statistics for monitoring
-- =============================================
CREATE TABLE TelemetryUsage (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ClientId NVARCHAR(50) NOT NULL,
    AppGuid NVARCHAR(50),
    EventType NVARCHAR(50) NOT NULL, -- event, exception, metric, pageview
    EventCount INT DEFAULT 1,
    UsageDate DATE NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    -- Foreign Key
    CONSTRAINT FK_TelemetryUsage_ClientApplications 
        FOREIGN KEY (ClientId) REFERENCES ClientApplications(ClientId) ON DELETE CASCADE,
    
    -- Unique constraint for aggregation
    CONSTRAINT UK_TelemetryUsage_Daily 
        UNIQUE (ClientId, AppGuid, EventType, UsageDate)
);

-- =============================================
-- Client Audit Log Table
-- Comprehensive audit trail
-- =============================================
CREATE TABLE ClientAuditLog (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ClientId NVARCHAR(50),
    Action NVARCHAR(100) NOT NULL, -- Created, Updated, Deleted, TokenGenerated, Approved, Rejected, etc.
    Details NVARCHAR(MAX), -- JSON with action details
    PerformedBy NVARCHAR(100) NOT NULL, -- Employee email
    PerformedAt DATETIME2 DEFAULT GETUTCDATE(),
    IPAddress NVARCHAR(45), -- For security tracking
    UserAgent NVARCHAR(500), -- Browser/app info
    
    -- Index for performance
    INDEX IX_ClientAuditLog_ClientId (ClientId),
    INDEX IX_ClientAuditLog_PerformedAt (PerformedAt DESC)
);
