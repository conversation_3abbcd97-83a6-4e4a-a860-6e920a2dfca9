-- =============================================
-- Stored Procedures for PowerApps Integration
-- These procedures handle complex operations that PowerApps cannot do directly
-- =============================================

-- =============================================
-- Generate Client Secret and Hash
-- PowerApps calls this to generate secure credentials
-- =============================================
CREATE PROCEDURE sp_GenerateClientCredentials
    @ClientId NVARCHAR(50) OUTPUT,
    @ClientSecret NVARCHAR(256) OUTPUT,
    @ClientSecretHash NVARCHAR(256) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Generate new ClientId (GUID)
    SET @ClientId = CAST(NEWID() AS NVARCHAR(50));
    
    -- Generate secure random secret (32 characters)
    DECLARE @RandomBytes VARBINARY(24);
    SET @RandomBytes = CRYPT_GEN_RANDOM(24);
    SET @ClientSecret = 'beca_' + LOWER(
        SUBSTRING(master.dbo.fn_varbintohexstr(@RandomBytes), 3, 48)
    );
    
    -- Generate SHA256 hash (simulated - actual hashing done in API)
    -- For now, we'll store a placeholder that the API will replace
    SET @ClientSecretHash = 'HASH_PLACEHOLDER_' + @ClientSecret;
END;

-- =============================================
-- Submit Client Registration Request
-- PowerApps calls this to submit a new registration
-- =============================================
CREATE PROCEDURE sp_SubmitClientRegistration
    @ClientName NVARCHAR(200),
    @ClientDescription NVARCHAR(1000),
    @ApplicationType NVARCHAR(50),
    @RequestedBy NVARCHAR(100),
    @BusinessJustification NVARCHAR(1000),
    --@ExpectedMonthlyVolume NVARCHAR(50),
    @ContactEmail NVARCHAR(200),
    @RegistrationId UNIQUEIDENTIFIER OUTPUT,
    @Result NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validate input
        IF @ClientName IS NULL OR LEN(TRIM(@ClientName)) = 0
        BEGIN
            SET @Result = 'ERROR: Client Name is required';
            RETURN;
        END
        
        IF @ApplicationType NOT IN ('Desktop', 'Web', 'Mobile', 'Service')
        BEGIN
            SET @Result = 'ERROR: Invalid Application Type';
            RETURN;
        END
        
        --IF @ExpectedMonthlyVolume NOT IN ('Low', 'Medium', 'High', 'Enterprise')
        --BEGIN
        --    SET @Result = 'ERROR: Invalid Expected Monthly Volume';
        --    RETURN;
        --END
        
        -- Check for duplicate name by same user (prevent spam)
        IF EXISTS (
            SELECT 1 FROM PendingClientRegistrations 
            WHERE ClientName = @ClientName 
            AND RequestedBy = @RequestedBy 
            AND Status = 'Pending'
            AND RequestedAt > DATEADD(DAY, -1, GETUTCDATE())
        )
        BEGIN
            SET @Result = 'ERROR: You already have a pending request for this application name';
            RETURN;
        END
        
        -- Check daily limit (5 requests per user per day)
        DECLARE @TodayCount INT;
        SELECT @TodayCount = COUNT(*)
        FROM PendingClientRegistrations
        WHERE RequestedBy = @RequestedBy
        AND CAST(RequestedAt AS DATE) = CAST(GETUTCDATE() AS DATE);
        
        IF @TodayCount >= 5
        BEGIN
            SET @Result = 'ERROR: Daily registration limit exceeded (5 per day)';
            RETURN;
        END
        
        -- Insert the registration request
        SET @RegistrationId = NEWID();
        
        INSERT INTO PendingClientRegistrations (
            Id, ClientName, ClientDescription, ApplicationType,
            RequestedBy, BusinessJustification, --ExpectedMonthlyVolume,
            ContactEmail, Status
        )
        VALUES (
            @RegistrationId, @ClientName, @ClientDescription, @ApplicationType,
            @RequestedBy, @BusinessJustification, --@ExpectedMonthlyVolume,
            @ContactEmail, 'Pending'
        );
        
        -- Construct JSON manually using FOR JSON PATH
        DECLARE @Details NVARCHAR(MAX);

        SELECT @Details = (
            SELECT 
                CAST(@RegistrationId AS NVARCHAR(50)) AS RegistrationId,
                @ClientName AS ClientName,
                @ApplicationType AS ApplicationType
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
        );

        -- Log the action
        INSERT INTO ClientAuditLog (Action, Details, PerformedBy)
        VALUES (
            'Registration Requested',
            @Details,
            @RequestedBy
        );
 
        SET @Result = 'SUCCESS: Registration request submitted successfully';
        
    END TRY
    BEGIN CATCH
        SET @Result = 'ERROR: ' + ERROR_MESSAGE();
        SET @RegistrationId = NULL;
    END CATCH
END;

-- =============================================
-- Approve Client Registration
-- Admin calls this from PowerApps to approve a registration
-- =============================================
CREATE PROCEDURE sp_ApproveClientRegistration
    @RegistrationId UNIQUEIDENTIFIER,
    @ApprovedBy NVARCHAR(100),
    @ReviewComments NVARCHAR(1000) = NULL,
    @ClientId NVARCHAR(50) OUTPUT,
    @ClientSecret NVARCHAR(256) OUTPUT,
    @Result NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if registration exists and is pending
        DECLARE @ClientName NVARCHAR(200), @ClientDescription NVARCHAR(1000),
                @ApplicationType NVARCHAR(50), @RequestedBy NVARCHAR(100),
                @BusinessJustification NVARCHAR(1000), --@ExpectedMonthlyVolume NVARCHAR(50),
                @ContactEmail NVARCHAR(200);
        
        SELECT 
            @ClientName = ClientName,
            @ClientDescription = ClientDescription,
            @ApplicationType = ApplicationType,
            @RequestedBy = RequestedBy,
            @BusinessJustification = BusinessJustification,
            --@ExpectedMonthlyVolume = ExpectedMonthlyVolume,
            @ContactEmail = ContactEmail
        FROM PendingClientRegistrations
        WHERE Id = @RegistrationId AND Status = 'Pending';
        
        IF @ClientName IS NULL
        BEGIN
            SET @Result = 'ERROR: Registration not found or already processed';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Generate credentials
        DECLARE @ClientSecretHash NVARCHAR(256);
        EXEC sp_GenerateClientCredentials @ClientId OUTPUT, @ClientSecret OUTPUT, @ClientSecretHash OUTPUT;
        
        -- Create the approved client
        INSERT INTO ClientApplications (
            ClientId, ClientName, ClientDescription, ApplicationType,
            ClientSecret, ClientSecretHash, CreatedBy, ApprovedBy, ApprovedAt,
            ContactEmail, BusinessJustification--, ExpectedMonthlyVolume
        )
        VALUES (
            @ClientId, @ClientName, @ClientDescription, @ApplicationType,
            @ClientSecret, @ClientSecretHash, @RequestedBy, @ApprovedBy, GETUTCDATE(),
            @ContactEmail, @BusinessJustification--, @ExpectedMonthlyVolume
        );
        
        -- Update the pending registration
        UPDATE PendingClientRegistrations
        SET Status = 'Approved',
            ReviewedBy = @ApprovedBy,
            ReviewedAt = GETUTCDATE(),
            ReviewComments = @ReviewComments,
            ApprovedClientId = @ClientId
        WHERE Id = @RegistrationId;
        
        -- Construct JSON manually using FOR JSON PATH
        DECLARE @Details NVARCHAR(MAX);

        SELECT @Details = (
            SELECT 
                CAST(@RegistrationId AS NVARCHAR(50)) AS RegistrationId,
                @ApprovedBy AS ApprovedBy,
                ISNULL(@ReviewComments, '') AS ReviewComments,
                @ClientName AS ClientName
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
        );

        -- Log the approval
        INSERT INTO ClientAuditLog (ClientId, Action, Details, PerformedBy)
        VALUES (
            @ClientId,
            'Registration Approved',
            @Details,
            @ApprovedBy
        );

        COMMIT TRANSACTION;
        SET @Result = 'SUCCESS: Client registration approved successfully';
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        SET @Result = 'ERROR: ' + ERROR_MESSAGE();
        SET @ClientId = NULL;
        SET @ClientSecret = NULL;
    END CATCH
END;

-- =============================================
-- Reject Client Registration
-- Admin calls this from PowerApps to reject a registration
-- =============================================
CREATE PROCEDURE sp_RejectClientRegistration
    @RegistrationId UNIQUEIDENTIFIER,
    @RejectedBy NVARCHAR(100),
    @ReviewComments NVARCHAR(1000),
    @Result NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if registration exists and is pending
        IF NOT EXISTS (
            SELECT 1 FROM PendingClientRegistrations 
            WHERE Id = @RegistrationId AND Status = 'Pending'
        )
        BEGIN
            SET @Result = 'ERROR: Registration not found or already processed';
            RETURN;
        END
        
        -- Update the pending registration
        UPDATE PendingClientRegistrations
        SET Status = 'Rejected',
            ReviewedBy = @RejectedBy,
            ReviewedAt = GETUTCDATE(),
            ReviewComments = @ReviewComments
        WHERE Id = @RegistrationId;
        
        -- Construct JSON manually using FOR JSON PATH
        DECLARE @Details NVARCHAR(MAX);

        SELECT @Details = (
            SELECT 
                CAST(@RegistrationId AS NVARCHAR(50)) AS RegistrationId,
                @RejectedBy AS RejectedBy,
                @ReviewComments AS ReviewComments
            FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
        );

        -- Log the rejection
        INSERT INTO ClientAuditLog (Action, Details, PerformedBy)
        VALUES (
            'Registration Rejected',
            @Details,
            @RejectedBy
        );

        SET @Result = 'SUCCESS: Client registration rejected';
        
    END TRY
    BEGIN CATCH
        SET @Result = 'ERROR: ' + ERROR_MESSAGE();
    END CATCH
END;
