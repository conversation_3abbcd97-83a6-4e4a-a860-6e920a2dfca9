# PowerApps Integration Workflow Documentation

## Overview
This document outlines the complete workflow for integrating a PowerApps application registration system with the BecaTracktion Telemetry Microservice, enabling developers to self-register their applications and obtain credentials for secure telemetry data transmission.

## Current Architecture Analysis

### Current State (Development)
- **Service**: `InMemoryAppVerificationService` (in-memory storage)
- **Database**: None (data stored in memory)
- **Client Registration**: Manual/hardcoded default clients
- **Credential Management**: Static credentials in code

### Target State (Production)
- **Service**: `SqlAppVerificationService` (database-backed)
- **Database**: Azure SQL Database
- **Client Registration**: PowerApps self-service portal
- **Credential Management**: Dynamic credential generation and management

## Complete Workflow Analysis

### Your Proposed Workflow ✅
1. **Developer registers the app in PowerApps** ✅
2. **Retrieves Client ID and Secret** ✅  
3. **Embeds credentials into application** ✅
4. **Application sends telemetry data** ✅
5. **App database stores metadata** ✅

### Missing/Additional Steps 🔧

#### **Pre-Registration Steps**
0. **Developer Authentication** - PowerApps user authentication/authorization
1. **Organization/Team Validation** - Ensure developer belongs to authorized organization

#### **Enhanced Registration Process**
2. **Application Details Collection**:
   - Application name and description
   - Application type (Desktop, Web, Mobile, Service)
   - Expected usage volume
   - Contact information
   - Business justification

3. **Approval Workflow** (Optional):
   - Admin review and approval
   - Automated approval for trusted developers
   - Notification system

#### **Post-Registration Steps**
6. **Credential Security**:
   - Secure credential delivery (not plain text)
   - Credential rotation capabilities
   - Expiration date management

7. **Monitoring & Management**:
   - Usage analytics and reporting
   - Rate limiting and quotas
   - Credential lifecycle management
   - Audit logging

8. **Developer Experience**:
   - Integration documentation
   - Code samples and SDKs
   - Testing environment access
   - Support and troubleshooting

## Detailed Implementation Requirements

### 1. Database Schema Design

#### Required Tables:
```sql
-- Client Applications
CREATE TABLE ClientApplications (
    ClientId NVARCHAR(50) PRIMARY KEY,
    ClientName NVARCHAR(200) NOT NULL,
    ClientDescription NVARCHAR(1000),
    ApplicationType NVARCHAR(50), -- Desktop, Web, Mobile, Service
    ClientSecretHash NVARCHAR(256) NOT NULL,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(100),
    LastUsedAt DATETIME2,
    ExpiresAt DATETIME2,
    AllowedRoles NVARCHAR(500), -- JSON array of roles
    ContactEmail NVARCHAR(200),
    OrganizationId NVARCHAR(50)
);

-- Registered App Instances
CREATE TABLE RegisteredAppInstances (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ClientId NVARCHAR(50) REFERENCES ClientApplications(ClientId),
    AppGuid NVARCHAR(50) NOT NULL,
    AppName NVARCHAR(200) NOT NULL,
    AppVersion NVARCHAR(50),
    OperatingSystem NVARCHAR(100),
    FirstRegisteredAt DATETIME2 DEFAULT GETUTCDATE(),
    LastUsedAt DATETIME2,
    IsActive BIT DEFAULT 1,
    AdditionalProperties NVARCHAR(MAX) -- JSON
);

-- Usage Analytics
CREATE TABLE TelemetryUsage (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ClientId NVARCHAR(50),
    AppGuid NVARCHAR(50),
    EventType NVARCHAR(50), -- event, exception, metric, pageview
    EventCount INT DEFAULT 1,
    UsageDate DATE,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE()
);

-- Audit Log
CREATE TABLE ClientAuditLog (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ClientId NVARCHAR(50),
    Action NVARCHAR(100), -- Created, Updated, Deleted, TokenGenerated, etc.
    Details NVARCHAR(MAX),
    PerformedBy NVARCHAR(100),
    PerformedAt DATETIME2 DEFAULT GETUTCDATE()
);
```

### 2. PowerApps Application Requirements

#### **Registration Form Fields**:
- Application Name* (required)
- Application Description
- Application Type* (dropdown: Desktop, Web, Mobile, Service)
- Contact Email*
- Organization/Department
- Expected Monthly Volume (dropdown ranges)
- Business Justification

#### **Generated Outputs**:
- Client ID (auto-generated GUID)
- Client Secret (secure random string)
- Integration Documentation Link
- Code Examples
- Test Environment Details

### 3. API Endpoints for PowerApps Integration

#### **New Controller**: `ClientManagementController`
```csharp
[Route("api/client-management")]
public class ClientManagementController : ControllerBase
{
    // Register new client application
    [HttpPost("register")]
    public async Task<ActionResult<ClientRegistrationResponse>> RegisterClient([FromBody] ClientRegistrationRequest request)

    // Get client details
    [HttpGet("{clientId}")]
    public async Task<ActionResult<ClientDetails>> GetClient(string clientId)

    // Update client details
    [HttpPut("{clientId}")]
    public async Task<ActionResult> UpdateClient(string clientId, [FromBody] ClientUpdateRequest request)

    // Regenerate client secret
    [HttpPost("{clientId}/regenerate-secret")]
    public async Task<ActionResult<SecretRegenerationResponse>> RegenerateSecret(string clientId)

    // Get usage analytics
    [HttpGet("{clientId}/usage")]
    public async Task<ActionResult<UsageAnalytics>> GetUsageAnalytics(string clientId)

    // Deactivate client
    [HttpDelete("{clientId}")]
    public async Task<ActionResult> DeactivateClient(string clientId)
}
```

### 4. Security Considerations

#### **PowerApps Security**:
- **Authentication**: Azure AD integration for developer authentication
- **Authorization**: Role-based access (Developer, Admin, Viewer)
- **Data Protection**: Encrypt sensitive data in transit and at rest
- **Audit Trail**: Log all registration and management activities

#### **API Security**:
- **Admin Authentication**: Separate admin API keys for PowerApps
- **Rate Limiting**: Prevent abuse of registration endpoints
- **Input Validation**: Comprehensive validation of all inputs
- **Secret Management**: Never store or transmit secrets in plain text

### 5. Implementation Phases

#### **Phase 1: Database Migration**
- [ ] Create Azure SQL Database schema
- [ ] Implement `SqlAppVerificationService`
- [ ] Migrate existing default clients to database
- [ ] Update dependency injection configuration

#### **Phase 2: Client Management API**
- [ ] Create `ClientManagementController`
- [ ] Implement registration endpoints
- [ ] Add authentication/authorization
- [ ] Create data models and DTOs

#### **Phase 3: PowerApps Development**
- [ ] Design PowerApps registration form
- [ ] Implement API integration
- [ ] Add approval workflow (if needed)
- [ ] Create admin dashboard

#### **Phase 4: Developer Experience**
- [ ] Create integration documentation
- [ ] Develop code samples
- [ ] Set up test environment
- [ ] Create troubleshooting guides

#### **Phase 5: Production Deployment**
- [ ] Security review and testing
- [ ] Performance testing
- [ ] Monitoring and alerting setup
- [ ] Go-live and user training

## Data Flow Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Developer     │    │    PowerApps     │    │  BecaTracktion API  │
│                 │    │   Registration   │    │                     │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
         │                       │                        │
         │ 1. Register App       │                        │
         ├──────────────────────►│                        │
         │                       │ 2. Create Client       │
         │                       ├───────────────────────►│
         │                       │                        │
         │                       │ 3. Return Credentials  │
         │                       │◄───────────────────────┤
         │ 4. Get Credentials    │                        │
         │◄──────────────────────┤                        │
         │                       │                        │
         │ 5. Embed in App       │                        │
         │                       │                        │
┌─────────────────┐              │                        │
│  Developer's    │              │                        │
│  Application    │              │                        │
└─────────────────┘              │                        │
         │                       │                        │
         │ 6. Send Telemetry     │                        │
         ├───────────────────────────────────────────────►│
         │                       │                        │
         │ 7. Store Metadata     │                        │
         │                       │                        │
┌─────────────────┐              │                        │
│  Azure SQL      │              │                        │
│  Database       │              │                        │
└─────────────────┘              │                        │
         ▲                       │                        │
         │ 8. Store Usage Data   │                        │
         └───────────────────────────────────────────────┘
```

## Next Steps Recommendations

### **Immediate Actions**:
1. **Review and approve** this workflow documentation
2. **Design the database schema** based on your specific requirements
3. **Create the Azure SQL Database** and tables
4. **Implement SqlAppVerificationService** to replace the in-memory version

### **PowerApps Development**:
1. **Start with a simple registration form** (Phase 3)
2. **Focus on core functionality first** (registration + credential generation)
3. **Add advanced features later** (approval workflows, analytics)

### **API Development Priority**:
1. **Client registration endpoint** (highest priority)
2. **Client details retrieval** (for PowerApps display)
3. **Secret regeneration** (for credential rotation)
4. **Usage analytics** (for monitoring)

## Questions for Consideration

1. **Approval Process**: Do you want automatic approval or manual admin review?
2. **Credential Expiration**: Should client credentials expire automatically?
3. **Usage Limits**: Do you want to implement rate limiting or quotas per client?
4. **Multi-tenancy**: Will different organizations need isolated access?
5. **Notification System**: Should developers receive email notifications for important events?

This workflow provides a solid foundation for your PowerApps integration while ensuring security, scalability, and maintainability.
