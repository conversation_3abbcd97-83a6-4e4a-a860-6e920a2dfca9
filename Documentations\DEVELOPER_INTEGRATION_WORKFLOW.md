# Developer Integration Workflow: Complete Step-by-Step Guide

## Overview
This document provides a complete step-by-step workflow for developers to register their applications and implement telemetry in their code, integrating with the existing First-Run App Verification system.

## Response to Your Comments

### 1. ✅ PowerApps Security (Azure AD)
**Status**: Already handled via SharePoint integration
- PowerApps in company SharePoint automatically inherits Azure AD authentication
- No additional security setup needed for PowerApps
- Developer authentication is handled by existing corporate identity

### 2. 🔍 First-Run App Verification Integration
**Current System Analysis**:
- ✅ **App GUID**: Currently auto-generated by client applications
- ✅ **Metadata Collection**: Happens automatically on first token request
- ✅ **Token Binding**: App GUID embedded in JWT token claims

**PowerApps Integration Plan**:
- **Pre-Registration**: Developer registers in PowerApps to get ClientId/Secret
- **First-Run**: Application still uses existing first-run verification for App GUID
- **Hybrid Approach**: PowerApps handles client credentials, first-run handles app instances

### 3. 📋 Detailed Step-by-Step Workflow
**Complete workflow from registration to implementation provided below**

## Complete Developer Workflow

### Phase 1: Application Registration (PowerApps)

#### Step 1: Access PowerApps Registration Portal
1. **Navigate** to company SharePoint site
2. **Open** BecaTracktion App Registration PowerApp
3. **Authenticate** using corporate Azure AD credentials

#### Step 2: Register New Application
1. **Fill Registration Form**:
   ```
   Application Name*: "My Awesome App"
   Description: "Desktop application for project management"
   Application Type*: [Desktop/Web/Mobile/Service]
   Contact Email*: <EMAIL>
   Department: Engineering
   Business Justification: "Track user interactions for UX improvement"
   ```

2. **Submit Registration**
   - Form validates required fields
   - PowerApps calls `/api/client-management/register`
   - System generates unique ClientId and ClientSecret

3. **Receive Credentials**
   ```
   ✅ Registration Successful!
   
   Client ID: my-awesome-app-client-2024
   Client Secret: [SECURE_RANDOM_STRING]
   
   ⚠️ IMPORTANT: Save these credentials securely!
   The secret will not be shown again.
   
   📚 Integration Guide: [Link to documentation]
   💻 Code Examples: [Link to samples]
   🧪 Test Environment: http://localhost:5000/swagger
   ```

### Phase 2: Application Implementation

#### Step 3: Install Client Library (Choose Your Platform)

**Option A: .NET Applications**
```bash
# Add NuGet package (when available)
dotnet add package BecaTracktion.Client

# Or copy TracktionClient.cs to your project
```

**Option B: Python Applications**
```bash
# Copy secure_telemetry_example.py to your project
# Install required packages
pip install requests uuid platform
```

**Option C: PowerShell Scripts**
```powershell
# Copy authentication functions from examples
# No additional packages needed
```

#### Step 4: Initialize Telemetry Client

**C# Implementation**:
```csharp
using BecaTracktion.Client;

// Initialize client with your credentials
var telemetryClient = new TracktionClient(
    baseUrl: "https://your-telemetry-service.com", // Production URL
    clientId: "my-awesome-app-client-2024",        // From PowerApps
    clientSecret: "your-secret-from-powerapps",    // From PowerApps
    appName: "My Awesome App"                      // Your app name
);

// The client will automatically:
// 1. Generate a unique App GUID for this installation
// 2. Collect app metadata (version, OS, etc.)
// 3. Handle first-run app verification
// 4. Manage JWT token lifecycle
```

**Python Implementation**:
```python
from secure_telemetry_client import SecureTelemetryClient

# Initialize client
client = SecureTelemetryClient(
    base_url="https://your-telemetry-service.com",
    client_id="my-awesome-app-client-2024",
    client_secret="your-secret-from-powerapps",
    app_name="My Awesome App"
)

# First run will automatically:
# - Generate unique App GUID
# - Register app instance
# - Obtain JWT token
```

#### Step 5: Implement Telemetry Tracking

**Track Events**:
```csharp
// Track user actions
await telemetryClient.TrackEventAsync("UserLogin", new Dictionary<string, string>
{
    ["UserId"] = "user123",
    ["LoginMethod"] = "SSO",
    ["Duration"] = "1.2s"
});

// Track feature usage
await telemetryClient.TrackEventAsync("FeatureUsed", new Dictionary<string, string>
{
    ["FeatureName"] = "ProjectCreation",
    ["ProjectType"] = "Construction",
    ["UserRole"] = "Engineer"
});
```

**Track Exceptions**:
```csharp
try
{
    // Your application code
    ProcessProject();
}
catch (Exception ex)
{
    // Track exceptions automatically
    await telemetryClient.TrackExceptionAsync(ex, new Dictionary<string, string>
    {
        ["Operation"] = "ProcessProject",
        ["ProjectId"] = "proj-123"
    });
    
    // Re-throw or handle as needed
    throw;
}
```

**Track Metrics**:
```csharp
// Track performance metrics
await telemetryClient.TrackMetricAsync("ProcessingTime", 1.5, new Dictionary<string, string>
{
    ["Operation"] = "DataImport",
    ["RecordCount"] = "1000"
});

// Track business metrics
await telemetryClient.TrackMetricAsync("ProjectsCreated", 1, new Dictionary<string, string>
{
    ["Department"] = "Engineering",
    ["ProjectType"] = "Infrastructure"
});
```

### Phase 3: First-Run App Verification (Automatic)

#### What Happens Behind the Scenes:

1. **App GUID Generation**:
   ```csharp
   // Automatically generated on first run
   string appGuid = GetOrCreateAppGuid(); // Creates persistent GUID
   // Example: "550e8400-e29b-41d4-a716-************"
   ```

2. **Metadata Collection**:
   ```csharp
   var metadata = new AppMetadata
   {
       AppGuid = appGuid,                    // Unique per installation
       AppName = "My Awesome App",           // From initialization
       AppVersion = GetAppVersion(),         // From assembly/config
       OperatingSystem = Environment.OSVersion.ToString()
   };
   ```

3. **First Token Request**:
   ```http
   POST /api/auth/token
   Content-Type: application/json
   
   {
     "clientId": "my-awesome-app-client-2024",
     "clientSecret": "your-secret",
     "appMetadata": {
       "appGuid": "550e8400-e29b-41d4-a716-************",
       "appName": "My Awesome App",
       "appVersion": "1.0.0",
       "operatingSystem": "Windows 11"
     }
   }
   ```

4. **Server Response**:
   ```json
   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "Bearer",
     "expires_in": 3600,
     "first_registration": true
   }
   ```

### Phase 4: Cross-Platform App GUID Strategy

#### App GUID Retrieval Methods by Platform:

**Windows (.NET)**:
```csharp
private string GetOrCreateAppGuid()
{
    // Option 1: Registry (Windows only)
    var registryKey = $@"SOFTWARE\{CompanyName}\{AppName}";
    var guid = Registry.CurrentUser.GetValue(registryKey + "\\AppGuid") as string;
    
    if (string.IsNullOrEmpty(guid))
    {
        guid = Guid.NewGuid().ToString();
        Registry.CurrentUser.CreateSubKey(registryKey).SetValue("AppGuid", guid);
    }
    
    return guid;
}
```

**Cross-Platform (.NET)**:
```csharp
private string GetOrCreateAppGuid()
{
    // Option 2: Local file storage (cross-platform)
    var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
    var guidFile = Path.Combine(appDataPath, "BecaTracktion", $"{AppName}.guid");
    
    if (File.Exists(guidFile))
    {
        return File.ReadAllText(guidFile).Trim();
    }
    
    var guid = Guid.NewGuid().ToString();
    Directory.CreateDirectory(Path.GetDirectoryName(guidFile));
    File.WriteAllText(guidFile, guid);
    return guid;
}
```

**Python**:
```python
import uuid
import os
import platform

def get_or_create_app_guid(app_name):
    # Cross-platform approach using user home directory
    home_dir = os.path.expanduser("~")
    guid_dir = os.path.join(home_dir, ".becatracktion")
    guid_file = os.path.join(guid_dir, f"{app_name}.guid")
    
    if os.path.exists(guid_file):
        with open(guid_file, 'r') as f:
            return f.read().strip()
    
    # Generate new GUID
    app_guid = str(uuid.uuid4())
    
    # Save to file
    os.makedirs(guid_dir, exist_ok=True)
    with open(guid_file, 'w') as f:
        f.write(app_guid)
    
    return app_guid
```

**PowerShell**:
```powershell
function Get-OrCreateAppGuid {
    param([string]$AppName)
    
    $guidPath = "$env:LOCALAPPDATA\BecaTracktion\$AppName.guid"
    
    if (Test-Path $guidPath) {
        return Get-Content $guidPath
    }
    
    $guid = [System.Guid]::NewGuid().ToString()
    $null = New-Item -Path (Split-Path $guidPath) -ItemType Directory -Force
    Set-Content -Path $guidPath -Value $guid
    
    return $guid
}
```

#### Fallback Strategy for App GUID:
If persistent storage fails, use machine-specific identifiers:
```csharp
private string GetFallbackAppGuid()
{
    // Combine multiple machine identifiers for uniqueness
    var machineId = Environment.MachineName;
    var userId = Environment.UserName;
    var appPath = Assembly.GetExecutingAssembly().Location;
    
    // Create deterministic GUID from machine characteristics
    var combined = $"{machineId}|{userId}|{appPath}|{AppName}";
    var hash = SHA256.HashData(Encoding.UTF8.GetBytes(combined));
    var guidBytes = hash.Take(16).ToArray();
    
    return new Guid(guidBytes).ToString();
}
```

## Testing Your Integration

### Step 6: Test in Development Environment

1. **Run Your Application**
2. **Check Logs** for first-run registration
3. **Verify Telemetry** in Application Insights
4. **Test Token Refresh** (wait 1 hour or modify expiration)

### Step 7: Production Deployment

1. **Update Base URL** to production telemetry service
2. **Verify Credentials** are correctly configured
3. **Monitor Telemetry Flow** in production
4. **Set up Alerts** for authentication failures

## Troubleshooting Guide

### Common Issues:

**401 Unauthorized**:
- Check ClientId and ClientSecret
- Verify App GUID persistence
- Ensure metadata headers match token claims

**App GUID Not Persisting**:
- Check file/registry permissions
- Verify storage path exists
- Use fallback GUID strategy

**Token Refresh Failures**:
- Check network connectivity
- Verify credentials haven't expired
- Review server logs for errors

This workflow ensures seamless integration while maintaining the existing first-run app verification system!
