using BecaTracktion.Models;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Text;

namespace BecaTracktion.Services
{
    /// <summary>
    /// In-memory implementation of app verification service for development/testing
    /// In production, this should be replaced with a database-backed implementation
    /// </summary>
    public class InMemoryAppVerificationService : IAppVerificationService
    {
        private readonly ConcurrentDictionary<string, ClientCredentials> _clientCredentials;
        private readonly ConcurrentDictionary<string, RegisteredApp> _registeredApps;
        private readonly ILogger<InMemoryAppVerificationService> _logger;

        public InMemoryAppVerificationService(ILogger<InMemoryAppVerificationService> logger)
        {
            _logger = logger;
            _clientCredentials = new ConcurrentDictionary<string, ClientCredentials>();
            _registeredApps = new ConcurrentDictionary<string, RegisteredApp>();
            
            // Initialize with some default clients for development
            InitializeDefaultClients();
        }

        public Task<bool> ValidateClientCredentialsAsync(string clientId, string clientSecret)
        {
            try
            {
                if (!_clientCredentials.TryGetValue(clientId, out var credentials))
                {
                    _logger.LogWarning("Client ID not found: {ClientId}", clientId);
                    return Task.FromResult(false);
                }

                if (!credentials.IsActive)
                {
                    _logger.LogWarning("Client is inactive: {ClientId}", clientId);
                    return Task.FromResult(false);
                }

                var hashedSecret = HashSecret(clientSecret);
                var isValid = credentials.ClientSecretHash == hashedSecret;

                if (isValid)
                {
                    credentials.LastUsedAt = DateTime.UtcNow;
                    _logger.LogInformation("Client credentials validated successfully: {ClientId}", clientId);
                }
                else
                {
                    _logger.LogWarning("Invalid client secret for: {ClientId}", clientId);
                }

                return Task.FromResult(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating client credentials for: {ClientId}", clientId);
                return Task.FromResult(false);
            }
        }

        public Task<(bool IsFirstRegistration, RegisteredApp RegisteredApp)> RegisterOrUpdateAppAsync(string clientId, AppMetadata metadata)
        {
            try
            {
                var key = $"{clientId}:{metadata.AppGuid}";
                
                if (_registeredApps.TryGetValue(key, out var existingApp))
                {
                    // Update existing registration
                    existingApp.Metadata = metadata;
                    existingApp.LastUsedAt = DateTime.UtcNow;
                    existingApp.TokensIssued++;
                    
                    _logger.LogInformation("Updated existing app registration: {ClientId}:{AppGuid}", clientId, metadata.AppGuid);
                    return Task.FromResult((false, existingApp));
                }
                else
                {
                    // Create new registration
                    var newApp = new RegisteredApp
                    {
                        ClientId = clientId,
                        Metadata = metadata,
                        RegisteredAt = DateTime.UtcNow,
                        LastUsedAt = DateTime.UtcNow,
                        TokensIssued = 1
                    };
                    
                    _registeredApps.TryAdd(key, newApp);
                    _logger.LogInformation("Registered new app: {ClientId}:{AppGuid}", clientId, metadata.AppGuid);
                    return Task.FromResult((true, newApp));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering/updating app: {ClientId}:{AppGuid}", clientId, metadata.AppGuid);
                throw;
            }
        }

        public Task<bool> ValidateTokenBindingAsync(string clientId, AppMetadata tokenMetadata, AppMetadata currentMetadata)
        {
            try
            {
                // Validate that the token's metadata matches the current request context
                var isValid = tokenMetadata.AppGuid == currentMetadata.AppGuid &&
                             tokenMetadata.AppName == currentMetadata.AppName;

                if (!isValid)
                {
                    _logger.LogWarning("Token binding validation failed for {ClientId}. Token metadata doesn't match current context", clientId);
                }

                return Task.FromResult(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token binding for: {ClientId}", clientId);
                return Task.FromResult(false);
            }
        }

        public Task<RegisteredApp?> GetRegisteredAppAsync(string clientId, string appGuid)
        {
            var key = $"{clientId}:{appGuid}";
            _registeredApps.TryGetValue(key, out var app);
            return Task.FromResult(app);
        }

        public Task UpdateLastUsedAsync(string clientId, string appGuid)
        {
            var key = $"{clientId}:{appGuid}";
            if (_registeredApps.TryGetValue(key, out var app))
            {
                app.LastUsedAt = DateTime.UtcNow;
            }
            return Task.CompletedTask;
        }

        public Task<ClientCredentials?> GetClientCredentialsAsync(string clientId)
        {
            _clientCredentials.TryGetValue(clientId, out var credentials);
            return Task.FromResult(credentials);
        }

        private void InitializeDefaultClients()
        {
            // Add some default clients for development/testing
            var defaultClients = new[]
            {
                new ClientCredentials
                {
                    ClientId = "revit-addin-client",
                    ClientSecretHash = HashSecret("revit-secret-2024"),
                    ClientName = "Revit Add-in Client",
                    AllowedRoles = new List<string> { "telemetry_client" }
                },
                new ClientCredentials
                {
                    ClientId = "python-script-client",
                    ClientSecretHash = HashSecret("python-secret-2024"),
                    ClientName = "Python Script Client",
                    AllowedRoles = new List<string> { "telemetry_client" }
                },
                new ClientCredentials
                {
                    ClientId = "test-client",
                    ClientSecretHash = HashSecret("test-secret-2024"),
                    ClientName = "Test Client",
                    AllowedRoles = new List<string> { "telemetry_client" }
                }
            };

            foreach (var client in defaultClients)
            {
                _clientCredentials.TryAdd(client.ClientId, client);
            }

            _logger.LogInformation("Initialized {Count} default clients", defaultClients.Length);
        }

        private static string HashSecret(string secret)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(secret));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
