using System.ComponentModel.DataAnnotations;

namespace BecaTracktion.Models
{
    /// <summary>
    /// Request model for JWT token issuance
    /// </summary>
    public class TokenRequest
    {
        /// <summary>
        /// Client identifier
        /// </summary>
        [Required]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Client secret
        /// </summary>
        [Required]
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// Application metadata for first-run verification and token binding
        /// </summary>
        [Required]
        public AppMetadata AppMetadata { get; set; } = new();

        /// <summary>
        /// Optional requested token expiration in minutes (default: 60)
        /// </summary>
        public int? ExpirationMinutes { get; set; }
    }
}
