using BecaTracktion.Models;
using BecaTracktion.Services;
using System.Text.Json;

namespace BecaTracktion.Middleware
{
    /// <summary>
    /// Middleware to validate JWT token binding with app metadata
    /// </summary>
    public class TokenBindingValidationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TokenBindingValidationMiddleware> _logger;

        public TokenBindingValidationMiddleware(RequestDelegate next, ILogger<TokenBindingValidationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IJwtTokenService jwtTokenService, IAppVerificationService appVerificationService)
        {
            // Only validate token binding for telemetry endpoints
            if (!context.Request.Path.StartsWithSegments("/api/telemetry") || 
                context.Request.Path.StartsWithSegments("/api/telemetry/health"))
            {
                await _next(context);
                return;
            }

            // Skip validation for non-authenticated requests (they will be handled by [Authorize])
            if (context.User?.Identity?.IsAuthenticated != true)
            {
                await _next(context);
                return;
            }

            try
            {
                // Extract token metadata from claims
                var tokenClientId = jwtTokenService.ExtractClientIdFromClaims(context.User);
                var tokenMetadata = jwtTokenService.ExtractAppMetadataFromClaims(context.User);

                if (string.IsNullOrEmpty(tokenClientId) || tokenMetadata == null)
                {
                    _logger.LogWarning("Token missing required metadata claims");
                    await WriteUnauthorizedResponse(context, "Token missing required metadata");
                    return;
                }

                // Extract current app metadata from request headers or body
                var currentMetadata = await ExtractCurrentAppMetadata(context);
                if (currentMetadata == null)
                {
                    _logger.LogWarning("Request missing app metadata for token binding validation");
                    await WriteUnauthorizedResponse(context, "Request missing app metadata");
                    return;
                }

                // Validate token binding
                var isValidBinding = await appVerificationService.ValidateTokenBindingAsync(
                    tokenClientId, tokenMetadata, currentMetadata);

                if (!isValidBinding)
                {
                    _logger.LogWarning("Token binding validation failed for client: {ClientId}, App: {AppGuid}", 
                        tokenClientId, tokenMetadata.AppGuid);
                    await WriteUnauthorizedResponse(context, "Token binding validation failed");
                    return;
                }

                // Update last used timestamp
                await appVerificationService.UpdateLastUsedAsync(tokenClientId, tokenMetadata.AppGuid);

                _logger.LogDebug("Token binding validation successful for client: {ClientId}, App: {AppGuid}", 
                    tokenClientId, tokenMetadata.AppGuid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token binding validation");
                await WriteUnauthorizedResponse(context, "Token validation error");
                return;
            }

            await _next(context);
        }

        private async Task<AppMetadata?> ExtractCurrentAppMetadata(HttpContext context)
        {
            try
            {
                // First, try to get metadata from custom headers
                var appGuid = context.Request.Headers["X-App-Guid"].FirstOrDefault();
                var appName = context.Request.Headers["X-App-Name"].FirstOrDefault();

                if (!string.IsNullOrEmpty(appGuid) && !string.IsNullOrEmpty(appName))
                {
                    return new AppMetadata
                    {
                        AppGuid = appGuid,
                        AppName = appName,
                        AppVersion = context.Request.Headers["X-App-Version"].FirstOrDefault(),
                        OperatingSystem = context.Request.Headers["X-Operating-System"].FirstOrDefault()
                    };
                }

                // If headers are not present, try to extract from request body (for POST requests)
                if (context.Request.Method == "POST" && context.Request.ContentType?.Contains("application/json") == true)
                {
                    // Enable buffering to allow multiple reads of the request body
                    context.Request.EnableBuffering();
                    
                    var body = await new StreamReader(context.Request.Body).ReadToEndAsync();
                    context.Request.Body.Position = 0; // Reset position for next middleware

                    if (!string.IsNullOrEmpty(body))
                    {
                        using var document = JsonDocument.Parse(body);
                        var root = document.RootElement;

                        // Look for app metadata in the request body
                        if (root.TryGetProperty("appMetadata", out var metadataElement))
                        {
                            return JsonSerializer.Deserialize<AppMetadata>(metadataElement.GetRawText());
                        }

                        // Look for individual metadata fields
                        var hasAppGuid = root.TryGetProperty("appGuid", out var appGuidElement);
                        var hasAppName = root.TryGetProperty("appName", out var appNameElement);

                        if (hasAppGuid && hasAppName)
                        {
                            return new AppMetadata
                            {
                                AppGuid = appGuidElement.GetString() ?? "",
                                AppName = appNameElement.GetString() ?? "",
                                AppVersion = root.TryGetProperty("appVersion", out var versionElement) ? versionElement.GetString() : null,
                                OperatingSystem = root.TryGetProperty("operatingSystem", out var osElement) ? osElement.GetString() : null
                            };
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting app metadata from request");
                return null;
            }
        }

        private async Task WriteUnauthorizedResponse(HttpContext context, string message)
        {
            context.Response.StatusCode = 401;
            context.Response.ContentType = "application/json";

            var response = new
            {
                error = "unauthorized",
                error_description = message,
                timestamp = DateTime.UtcNow
            };

            await context.Response.WriteAsync(JsonSerializer.Serialize(response));
        }
    }

    /// <summary>
    /// Extension method to register the token binding validation middleware
    /// </summary>
    public static class TokenBindingValidationMiddlewareExtensions
    {
        public static IApplicationBuilder UseTokenBindingValidation(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TokenBindingValidationMiddleware>();
        }
    }
}
