using System.ComponentModel.DataAnnotations;

namespace BecaTracktion.Models.PowerApps
{
    /// <summary>
    /// Model for displaying client details in PowerApps
    /// </summary>
    public class ClientDetailsModel
    {
        /// <summary>
        /// Client identifier
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Client name
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// Client description
        /// </summary>
        public string? ClientDescription { get; set; }

        /// <summary>
        /// Application type
        /// </summary>
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// Who created the client
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the client was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Who approved the client
        /// </summary>
        public string? ApprovedBy { get; set; }

        /// <summary>
        /// When the client was approved
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// Whether the client is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// When the client expires (if applicable)
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Contact email
        /// </summary>
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// Business justification
        /// </summary>
        public string? BusinessJustification { get; set; }

        /// <summary>
        /// Expected monthly volume
        /// </summary>
        public string? ExpectedMonthlyVolume { get; set; }

        /// <summary>
        /// Last time the client was used
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// Computed expiration status
        /// </summary>
        public string ExpirationStatus { get; set; } = "Active";

        /// <summary>
        /// Overall status
        /// </summary>
        public string OverallStatus { get; set; } = "Active";

        /// <summary>
        /// Days since created
        /// </summary>
        public int DaysSinceCreated { get; set; }

        /// <summary>
        /// Days since last used
        /// </summary>
        public int? DaysSinceLastUsed { get; set; }

        /// <summary>
        /// Number of active app instances
        /// </summary>
        public int ActiveAppInstances { get; set; }

        /// <summary>
        /// Total tokens issued
        /// </summary>
        public int TotalTokensIssued { get; set; }
    }

    /// <summary>
    /// Model for updating client details
    /// </summary>
    public class ClientUpdateRequest
    {
        /// <summary>
        /// Client ID to update
        /// </summary>
        [Required]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Updated client name
        /// </summary>
        [StringLength(200)]
        public string? ClientName { get; set; }

        /// <summary>
        /// Updated client description
        /// </summary>
        [StringLength(1000)]
        public string? ClientDescription { get; set; }

        /// <summary>
        /// Updated contact email
        /// </summary>
        [EmailAddress]
        [StringLength(200)]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// Whether the client should be active
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// New expiration date (optional)
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Who is making the update
        /// </summary>
        [Required]
        [EmailAddress]
        public string UpdatedBy { get; set; } = string.Empty;

        /// <summary>
        /// Reason for the update
        /// </summary>
        [StringLength(500)]
        public string? UpdateReason { get; set; }
    }

    /// <summary>
    /// Model for secret regeneration request
    /// </summary>
    public class SecretRegenerationRequest
    {
        /// <summary>
        /// Client ID to regenerate secret for
        /// </summary>
        [Required]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Admin requesting the regeneration
        /// </summary>
        [Required]
        [EmailAddress]
        public string RequestedBy { get; set; } = string.Empty;

        /// <summary>
        /// Reason for regeneration
        /// </summary>
        [Required]
        [StringLength(500, MinimumLength = 10)]
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for secret regeneration
    /// </summary>
    public class SecretRegenerationResponse
    {
        /// <summary>
        /// Client ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// New client secret (display once only)
        /// </summary>
        public string NewClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// When the secret was regenerated
        /// </summary>
        public DateTime RegeneratedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Status message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Warning about updating applications
        /// </summary>
        public string Warning { get; set; } = "All applications using this client must be updated with the new secret immediately.";
    }

    /// <summary>
    /// Model for usage analytics display in PowerApps
    /// </summary>
    public class UsageAnalyticsModel
    {
        /// <summary>
        /// Client ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Client name
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// Application type
        /// </summary>
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// Event type (event, exception, metric, pageview)
        /// </summary>
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// Usage date
        /// </summary>
        public DateTime UsageDate { get; set; }

        /// <summary>
        /// Number of events
        /// </summary>
        public int EventCount { get; set; }

        /// <summary>
        /// Usage year
        /// </summary>
        public int UsageYear { get; set; }

        /// <summary>
        /// Usage month
        /// </summary>
        public int UsageMonth { get; set; }

        /// <summary>
        /// Usage month name
        /// </summary>
        public string UsageMonthName { get; set; } = string.Empty;

        /// <summary>
        /// Usage week
        /// </summary>
        public int UsageWeek { get; set; }

        /// <summary>
        /// Running total
        /// </summary>
        public int RunningTotal { get; set; }

        /// <summary>
        /// Monthly total
        /// </summary>
        public int MonthlyTotal { get; set; }
    }

    /// <summary>
    /// Model for app instance summary in PowerApps
    /// </summary>
    public class AppInstanceSummaryModel
    {
        /// <summary>
        /// Instance ID
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Client ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Client name
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// App GUID
        /// </summary>
        public string AppGuid { get; set; } = string.Empty;

        /// <summary>
        /// App name
        /// </summary>
        public string AppName { get; set; } = string.Empty;

        /// <summary>
        /// App version
        /// </summary>
        public string? AppVersion { get; set; }

        /// <summary>
        /// Operating system
        /// </summary>
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// When first registered
        /// </summary>
        public DateTime FirstRegisteredAt { get; set; }

        /// <summary>
        /// Last used timestamp
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// Whether active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Number of tokens issued
        /// </summary>
        public int TokensIssued { get; set; }

        /// <summary>
        /// Days since registration
        /// </summary>
        public int DaysSinceRegistration { get; set; }

        /// <summary>
        /// Days since last used
        /// </summary>
        public int? DaysSinceLastUsed { get; set; }

        /// <summary>
        /// Activity status
        /// </summary>
        public string ActivityStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// Model for audit log display in PowerApps
    /// </summary>
    public class AuditLogModel
    {
        /// <summary>
        /// Audit log entry ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// Client ID (if applicable)
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// Client name (if applicable)
        /// </summary>
        public string? ClientName { get; set; }

        /// <summary>
        /// Action performed
        /// </summary>
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Who performed the action
        /// </summary>
        public string PerformedBy { get; set; } = string.Empty;

        /// <summary>
        /// When the action was performed
        /// </summary>
        public DateTime PerformedAt { get; set; }

        /// <summary>
        /// Action category
        /// </summary>
        public string ActionCategory { get; set; } = string.Empty;

        /// <summary>
        /// Minutes ago
        /// </summary>
        public int MinutesAgo { get; set; }

        /// <summary>
        /// Time ago description
        /// </summary>
        public string TimeAgoDescription { get; set; } = string.Empty;
    }
}
