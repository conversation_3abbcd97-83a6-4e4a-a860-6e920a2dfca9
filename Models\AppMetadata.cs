namespace BecaTracktion.Models
{
    /// <summary>
    /// Represents application metadata collected during first-run verification
    /// </summary>
    public class AppMetadata
    {
        /// <summary>
        /// Unique identifier for the application instance
        /// </summary>
        public string AppGuid { get; set; } = string.Empty;

        /// <summary>
        /// Name of the application
        /// </summary>
        public string AppName { get; set; } = string.Empty;

        /// <summary>
        /// Application version
        /// </summary>
        public string? AppVersion { get; set; }

        /// <summary>
        /// Operating system information
        /// </summary>
        public string? OperatingSystem { get; set; }

        /// <summary>
        /// Additional metadata properties
        /// </summary>
        public Dictionary<string, string>? AdditionalProperties { get; set; }

        /// <summary>
        /// Timestamp when the metadata was collected
        /// </summary>
        public DateTime CollectedAt { get; set; } = DateTime.UtcNow;
    }
}
