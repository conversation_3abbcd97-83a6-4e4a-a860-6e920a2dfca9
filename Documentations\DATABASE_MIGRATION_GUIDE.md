# Database Migration Guide: From In-Memory to Azure SQL

## Overview
This guide provides step-by-step instructions for migrating the BecaTracktion Telemetry Microservice from the current in-memory storage (`InMemoryAppVerificationService`) to Azure SQL Database, while preserving the existing First-Run App Verification system.

## Integration with Existing First-Run App Verification

### Current System Preservation
The existing first-run app verification system will be **preserved and enhanced**:
- ✅ **App GUID Generation**: Still handled by client applications (cross-platform compatible)
- ✅ **Metadata Collection**: Still automatic on first token request
- ✅ **Token Binding**: App GUID still embedded in JWT token claims
- 🆕 **PowerApps Integration**: Adds client credential management layer

### Hybrid Architecture
```
PowerApps Registration → Client Credentials (ClientId/Secret)
                              ↓
First-Run Verification → App Instance Metadata (AppGuid/Name/Version)
                              ↓
Token Generation → JWT with both Client + App metadata
```

## Prerequisites
- Azure SQL Database instance created and accessible
- Connection string configured in `appsettings.json`
- Entity Framework Core packages installed

## Step 1: Database Schema Creation

### SQL Script for Initial Tables
```sql
-- Create the main database schema
CREATE SCHEMA [telemetry] AUTHORIZATION [dbo];
GO

-- Client Applications Table
CREATE TABLE [telemetry].[ClientApplications] (
    [ClientId] NVARCHAR(50) NOT NULL PRIMARY KEY,
    [ClientName] NVARCHAR(200) NOT NULL,
    [ClientDescription] NVARCHAR(1000) NULL,
    [ApplicationType] NVARCHAR(50) NULL DEFAULT 'Desktop',
    [ClientSecretHash] NVARCHAR(256) NOT NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] NVARCHAR(100) NULL,
    [LastUsedAt] DATETIME2 NULL,
    [ExpiresAt] DATETIME2 NULL,
    [AllowedRoles] NVARCHAR(500) NOT NULL DEFAULT '["telemetry_client"]',
    [ContactEmail] NVARCHAR(200) NULL,
    [OrganizationId] NVARCHAR(50) NULL,
    [CreatedVia] NVARCHAR(50) DEFAULT 'Manual' -- Manual, PowerApps, API
);

-- Registered App Instances Table (First-Run App Verification Data)
CREATE TABLE [telemetry].[RegisteredAppInstances] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [ClientId] NVARCHAR(50) NOT NULL,
    [AppGuid] NVARCHAR(50) NOT NULL, -- Generated by client app on first run
    [AppName] NVARCHAR(200) NOT NULL,
    [AppVersion] NVARCHAR(50) NULL,
    [OperatingSystem] NVARCHAR(100) NULL,
    [FirstRegisteredAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [LastUsedAt] DATETIME2 NULL,
    [IsActive] BIT NOT NULL DEFAULT 1,
    [AdditionalProperties] NVARCHAR(MAX) NULL, -- JSON format for extensibility
    [InstallationPath] NVARCHAR(500) NULL, -- Where app is installed (optional)
    [GuidGenerationMethod] NVARCHAR(50) DEFAULT 'Auto', -- Auto, Registry, File, Fallback
    CONSTRAINT [FK_RegisteredAppInstances_ClientApplications]
        FOREIGN KEY ([ClientId]) REFERENCES [telemetry].[ClientApplications]([ClientId])
);

-- Usage Analytics Table
CREATE TABLE [telemetry].[TelemetryUsage] (
    [Id] BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [ClientId] NVARCHAR(50) NOT NULL,
    [AppGuid] NVARCHAR(50) NULL,
    [EventType] NVARCHAR(50) NOT NULL, -- event, exception, metric, pageview, flush
    [EventCount] INT NOT NULL DEFAULT 1,
    [UsageDate] DATE NOT NULL,
    [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT [FK_TelemetryUsage_ClientApplications] 
        FOREIGN KEY ([ClientId]) REFERENCES [telemetry].[ClientApplications]([ClientId])
);

-- Client Audit Log Table
CREATE TABLE [telemetry].[ClientAuditLog] (
    [Id] BIGINT IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [ClientId] NVARCHAR(50) NULL,
    [Action] NVARCHAR(100) NOT NULL,
    [Details] NVARCHAR(MAX) NULL,
    [PerformedBy] NVARCHAR(100) NULL,
    [PerformedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [IpAddress] NVARCHAR(45) NULL,
    [UserAgent] NVARCHAR(500) NULL
);

-- Create indexes for performance
CREATE INDEX [IX_RegisteredAppInstances_ClientId] ON [telemetry].[RegisteredAppInstances]([ClientId]);
CREATE INDEX [IX_RegisteredAppInstances_AppGuid] ON [telemetry].[RegisteredAppInstances]([AppGuid]);
CREATE INDEX [IX_RegisteredAppInstances_ClientId_AppGuid] ON [telemetry].[RegisteredAppInstances]([ClientId], [AppGuid]); -- For token binding validation
CREATE INDEX [IX_TelemetryUsage_ClientId_Date] ON [telemetry].[TelemetryUsage]([ClientId], [UsageDate]);
CREATE INDEX [IX_ClientAuditLog_ClientId] ON [telemetry].[ClientAuditLog]([ClientId]);
CREATE INDEX [IX_ClientApplications_IsActive] ON [telemetry].[ClientApplications]([IsActive]);
```

## Step 2: Data Migration Script

### Migrate Existing Default Clients
```sql
-- Insert the current default clients from InMemoryAppVerificationService
INSERT INTO [telemetry].[ClientApplications] 
([ClientId], [ClientName], [ClientDescription], [ApplicationType], [ClientSecretHash], [AllowedRoles], [CreatedBy], [CreatedVia])
VALUES 
('revit-addin-client', 'Revit Add-in Client', 'Default client for Revit add-in applications', 'Desktop', 
 -- Hash for 'revit-secret-2024' (you'll need to generate this using your HashSecret method)
 'HASH_VALUE_HERE', '["telemetry_client"]', 'System Migration', 'Manual'),

('python-script-client', 'Python Script Client', 'Default client for Python script applications', 'Desktop',
 -- Hash for 'python-secret-2024'
 'HASH_VALUE_HERE', '["telemetry_client"]', 'System Migration', 'Manual'),

('test-client', 'Test Client', 'Default client for testing and development', 'Desktop',
 -- Hash for 'test-secret-2024'
 'HASH_VALUE_HERE', '["telemetry_client"]', 'System Migration', 'Manual');
```

## Step 3: Connection String Configuration

### Update appsettings.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-server.database.windows.net;Database=your-database;User Id=your-username;Password=your-password;Encrypt=True;TrustServerCertificate=False;"
  },
  "Jwt": {
    "SecretKey": "your-jwt-secret-key",
    "Issuer": "BecaTracktion",
    "Audience": "BecaTracktion-Clients"
  }
}
```

## Step 4: Install Required NuGet Packages

```xml
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
<PackageReference Include="System.Text.Json" Version="8.0.0" />
```

## Step 5: Create Entity Models

### ClientApplication Entity
```csharp
public class ClientApplication
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientName { get; set; } = string.Empty;
    public string? ClientDescription { get; set; }
    public string? ApplicationType { get; set; }
    public string ClientSecretHash { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public string? CreatedBy { get; set; }
    public DateTime? LastUsedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string AllowedRoles { get; set; } = "[\"telemetry_client\"]"; // JSON array
    public string? ContactEmail { get; set; }
    public string? OrganizationId { get; set; }
    public string CreatedVia { get; set; } = "Manual";

    // Navigation properties
    public virtual ICollection<RegisteredAppInstance> RegisteredAppInstances { get; set; } = new List<RegisteredAppInstance>();
    public virtual ICollection<TelemetryUsage> TelemetryUsages { get; set; } = new List<TelemetryUsage>();
}
```

### RegisteredAppInstance Entity
```csharp
public class RegisteredAppInstance
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string ClientId { get; set; } = string.Empty;
    public string AppGuid { get; set; } = string.Empty;
    public string AppName { get; set; } = string.Empty;
    public string? AppVersion { get; set; }
    public string? OperatingSystem { get; set; }
    public DateTime FirstRegisteredAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastUsedAt { get; set; }
    public bool IsActive { get; set; } = true;
    public string? AdditionalProperties { get; set; } // JSON

    // Navigation property
    public virtual ClientApplication ClientApplication { get; set; } = null!;
}
```

## Step 6: Create DbContext

### TelemetryDbContext
```csharp
public class TelemetryDbContext : DbContext
{
    public TelemetryDbContext(DbContextOptions<TelemetryDbContext> options) : base(options) { }

    public DbSet<ClientApplication> ClientApplications { get; set; }
    public DbSet<RegisteredAppInstance> RegisteredAppInstances { get; set; }
    public DbSet<TelemetryUsage> TelemetryUsages { get; set; }
    public DbSet<ClientAuditLog> ClientAuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("telemetry");

        // Configure ClientApplication
        modelBuilder.Entity<ClientApplication>(entity =>
        {
            entity.HasKey(e => e.ClientId);
            entity.Property(e => e.ClientId).HasMaxLength(50);
            entity.Property(e => e.ClientName).HasMaxLength(200).IsRequired();
            entity.Property(e => e.ClientSecretHash).HasMaxLength(256).IsRequired();
            entity.Property(e => e.AllowedRoles).HasMaxLength(500).IsRequired();
        });

        // Configure RegisteredAppInstance
        modelBuilder.Entity<RegisteredAppInstance>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ClientId).HasMaxLength(50).IsRequired();
            entity.Property(e => e.AppGuid).HasMaxLength(50).IsRequired();
            entity.Property(e => e.AppName).HasMaxLength(200).IsRequired();
            
            entity.HasOne(d => d.ClientApplication)
                  .WithMany(p => p.RegisteredAppInstances)
                  .HasForeignKey(d => d.ClientId);
        });

        base.OnModelCreating(modelBuilder);
    }
}
```

## Step 7: Update Program.cs

### Add DbContext Registration
```csharp
// Add Entity Framework
builder.Services.AddDbContext<TelemetryDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Replace InMemoryAppVerificationService with SqlAppVerificationService
builder.Services.AddScoped<IAppVerificationService, SqlAppVerificationService>();
```

## Next Steps

1. **Create the SqlAppVerificationService implementation**
2. **Test the database connection and basic operations**
3. **Implement data migration from in-memory to database**
4. **Add comprehensive error handling and logging**
5. **Create database backup and recovery procedures**

This migration will provide a solid foundation for the PowerApps integration and production scalability.
