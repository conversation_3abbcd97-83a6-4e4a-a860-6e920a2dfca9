-- =============================================
-- Seed Default Clients
-- Migrates the existing hardcoded clients to the database
-- =============================================

-- Insert default clients that were previously hardcoded
INSERT INTO ClientApplications (
    ClientId, ClientName, ClientDescription, ApplicationType,
    ClientSecret, ClientSecretHash, CreatedBy, ApprovedBy, ApprovedAt,
    ContactEmail, BusinessJustification, ExpectedMonthlyVolume, IsActive
)
VALUES 
(
    'revit-addin-client',
    'Revit Add-in Client',
    'Default client for Revit add-in applications',
    'Desktop',
    'revit-secret-2024',
    -- Note: The actual hash will be computed by the API service
    'HASH_PLACEHOLDER_revit-secret-2024',
    '<EMAIL>',
    '<EMAIL>',
    GETUTCDATE(),
    '<EMAIL>',
    'Default client for Revit add-in development and testing',
    'Medium',
    1
),
(
    'python-script-client',
    'Python Script Client',
    'Default client for Python automation scripts',
    'Service',
    'python-secret-2024',
    -- Note: The actual hash will be computed by the API service
    'HASH_PLACEHOLDER_python-secret-2024',
    '<EMAIL>',
    '<EMAIL>',
    GETUTCDATE(),
    '<EMAIL>',
    'Default client for Python script automation and telemetry',
    'Medium',
    1
),
(
    'test-client',
    'Test Client',
    'Default client for testing and development',
    'Service',
    'test-secret-2024',
    -- Note: The actual hash will be computed by the API service
    'HASH_PLACEHOLDER_test-secret-2024',
    '<EMAIL>',
    '<EMAIL>',
    GETUTCDATE(),
    '<EMAIL>',
    'Default client for API testing and development purposes',
    'Low',
    1
);

-- Log the seeding action
INSERT INTO ClientAuditLog (ClientId, Action, Details, PerformedBy)
VALUES 
(
    'revit-addin-client',
    'Default Client Seeded',
    JSON_OBJECT('source', 'database_migration', 'type', 'default_client'),
    '<EMAIL>'
),
(
    'python-script-client',
    'Default Client Seeded',
    JSON_OBJECT('source', 'database_migration', 'type', 'default_client'),
    '<EMAIL>'
),
(
    'test-client',
    'Default Client Seeded',
    JSON_OBJECT('source', 'database_migration', 'type', 'default_client'),
    '<EMAIL>'
);

-- Display the seeded clients
SELECT 
    ClientId,
    ClientName,
    ApplicationType,
    CreatedBy,
    CreatedAt,
    IsActive
FROM ClientApplications
WHERE CreatedBy = '<EMAIL>';

PRINT 'Default clients have been successfully seeded into the database.';
PRINT 'Note: Client secret hashes will be updated when the SqlAppVerificationService is implemented.';
