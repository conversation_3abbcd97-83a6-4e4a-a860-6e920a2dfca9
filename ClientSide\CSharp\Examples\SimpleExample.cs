using System;
using System.Collections.Generic;
using System.Threading;

// Choose the appropriate namespace based on your target framework
#if NET8_0_OR_GREATER
using TracktionClientCore;
#else
using TracktionClientFramework;
#endif

namespace BecaTracktionExamples
{
    /// <summary>
    /// Simple example demonstrating BecaTracktion client usage
    /// This example shows how to:
    /// 1. Initialize the client with authentication
    /// 2. Test the connection
    /// 3. Send various types of telemetry
    /// 4. Handle errors gracefully
    /// </summary>
    class SimpleExample
    {
        // TODO: Replace these with your actual credentials from PowerApps registration
        private const string API_BASE_URL = "https://your-api.azurewebsites.net";
        private const string CLIENT_ID = "your-client-id-from-powerapps";
        private const string CLIENT_SECRET = "your-client-secret-from-powerapps";
        
        static void Main(string[] args)
        {
            Console.WriteLine("🚀 BecaTracktion Client Example");
            Console.WriteLine("================================");
            
            // Initialize the telemetry client
            using (var client = new TracktionClient(API_BASE_URL, CLIENT_ID, CLIENT_SECRET, "Example Application"))
            {
                // Test the connection first
                Console.WriteLine("\n📡 Testing connection...");
                if (!client.TestConnection())
                {
                    Console.WriteLine("❌ Connection failed! Please check your credentials and network connection.");
                    Console.WriteLine("Press any key to exit...");
                    Console.ReadKey();
                    return;
                }
                Console.WriteLine("✅ Connection successful!");
                
                // Demonstrate different types of telemetry
                DemonstrateEventTracking(client);
                DemonstrateMetricTracking(client);
                DemonstrateExceptionTracking(client);
                DemonstratePageViewTracking(client);
                
                // Flush all telemetry before closing
                Console.WriteLine("\n🔄 Flushing telemetry...");
                if (client.Flush())
                {
                    Console.WriteLine("✅ Telemetry flushed successfully!");
                }
                else
                {
                    Console.WriteLine("⚠️ Failed to flush telemetry");
                }
            }
            
            Console.WriteLine("\n🎉 Example completed! Press any key to exit...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// Demonstrate event tracking with various scenarios
        /// </summary>
        static void DemonstrateEventTracking(TracktionClient client)
        {
            Console.WriteLine("\n📊 Demonstrating Event Tracking:");
            
            // Simple event
            Console.WriteLine("  • Tracking simple event...");
            if (client.TrackEvent("ApplicationStarted"))
            {
                Console.WriteLine("    ✅ Simple event tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track simple event");
            }
            
            // Event with properties
            Console.WriteLine("  • Tracking event with properties...");
            var properties = new Dictionary<string, string>
            {
                ["UserAction"] = "ButtonClick",
                ["ButtonName"] = "ProcessData",
                ["Screen"] = "MainWindow"
            };
            
            if (client.TrackEvent("UserInteraction", properties))
            {
                Console.WriteLine("    ✅ Event with properties tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track event with properties");
            }
            
            // Event with properties and metrics
            Console.WriteLine("  • Tracking event with properties and metrics...");
            var metrics = new Dictionary<string, double>
            {
                ["ProcessingTimeMs"] = 1250.5,
                ["ItemsProcessed"] = 42,
                ["MemoryUsageMB"] = 128.7
            };
            
            if (client.TrackEvent("DataProcessingCompleted", properties, metrics))
            {
                Console.WriteLine("    ✅ Event with properties and metrics tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track event with properties and metrics");
            }
        }
        
        /// <summary>
        /// Demonstrate metric tracking
        /// </summary>
        static void DemonstrateMetricTracking(TracktionClient client)
        {
            Console.WriteLine("\n📈 Demonstrating Metric Tracking:");
            
            // Simple metric
            Console.WriteLine("  • Tracking simple metric...");
            if (client.TrackMetric("ResponseTime", 245.8))
            {
                Console.WriteLine("    ✅ Simple metric tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track simple metric");
            }
            
            // Metric with properties
            Console.WriteLine("  • Tracking metric with properties...");
            var metricProperties = new Dictionary<string, string>
            {
                ["Operation"] = "DatabaseQuery",
                ["QueryType"] = "SELECT",
                ["Database"] = "ProductionDB"
            };
            
            if (client.TrackMetric("QueryExecutionTime", 89.3, metricProperties))
            {
                Console.WriteLine("    ✅ Metric with properties tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track metric with properties");
            }
        }
        
        /// <summary>
        /// Demonstrate exception tracking
        /// </summary>
        static void DemonstrateExceptionTracking(TracktionClient client)
        {
            Console.WriteLine("\n🚨 Demonstrating Exception Tracking:");
            
            try
            {
                // Simulate an exception
                throw new InvalidOperationException("This is a simulated exception for demonstration purposes");
            }
            catch (Exception ex)
            {
                Console.WriteLine("  • Tracking exception...");
                var exceptionProperties = new Dictionary<string, string>
                {
                    ["Operation"] = "DataValidation",
                    ["InputSource"] = "UserForm",
                    ["Severity"] = "Medium"
                };
                
                if (client.TrackException(ex, "DemonstrateExceptionTracking", exceptionProperties))
                {
                    Console.WriteLine("    ✅ Exception tracked successfully");
                }
                else
                {
                    Console.WriteLine("    ❌ Failed to track exception");
                }
            }
        }
        
        /// <summary>
        /// Demonstrate page view tracking (useful for UI applications)
        /// </summary>
        static void DemonstratePageViewTracking(TracktionClient client)
        {
            Console.WriteLine("\n👁️ Demonstrating Page View Tracking:");
            
            // Simple page view
            Console.WriteLine("  • Tracking simple page view...");
            if (client.TrackPageView("MainWindow"))
            {
                Console.WriteLine("    ✅ Simple page view tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track simple page view");
            }
            
            // Page view with properties
            Console.WriteLine("  • Tracking page view with properties...");
            var pageProperties = new Dictionary<string, string>
            {
                ["NavigationSource"] = "Menu",
                ["UserRole"] = "Administrator",
                ["SessionId"] = Guid.NewGuid().ToString()
            };
            
            if (client.TrackPageView("SettingsDialog", pageProperties))
            {
                Console.WriteLine("    ✅ Page view with properties tracked");
            }
            else
            {
                Console.WriteLine("    ❌ Failed to track page view with properties");
            }
        }
    }
    
    /// <summary>
    /// Advanced example showing real-world usage patterns
    /// </summary>
    class AdvancedExample
    {
        private TracktionClient _telemetryClient;
        
        public AdvancedExample()
        {
            // Initialize telemetry client (in real app, get credentials from config)
            _telemetryClient = new TracktionClient(
                API_BASE_URL, 
                CLIENT_ID, 
                CLIENT_SECRET, 
                "Advanced Example App"
            );
        }
        
        /// <summary>
        /// Example of tracking a long-running operation
        /// </summary>
        public void ProcessLargeDataset(string datasetPath)
        {
            var startTime = DateTime.UtcNow;
            var operationId = Guid.NewGuid().ToString();
            
            try
            {
                // Track operation start
                _telemetryClient.TrackEvent("OperationStarted", new Dictionary<string, string>
                {
                    ["OperationType"] = "DatasetProcessing",
                    ["OperationId"] = operationId,
                    ["DatasetPath"] = datasetPath
                });
                
                // Simulate processing work
                Thread.Sleep(2000); // Simulate 2 seconds of work
                
                // Track progress metrics
                _telemetryClient.TrackMetric("ProcessingProgress", 50.0, new Dictionary<string, string>
                {
                    ["OperationId"] = operationId,
                    ["Stage"] = "DataValidation"
                });
                
                Thread.Sleep(1000); // More work
                
                _telemetryClient.TrackMetric("ProcessingProgress", 100.0, new Dictionary<string, string>
                {
                    ["OperationId"] = operationId,
                    ["Stage"] = "DataTransformation"
                });
                
                // Track successful completion
                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;
                _telemetryClient.TrackEvent("OperationCompleted", 
                    new Dictionary<string, string>
                    {
                        ["OperationType"] = "DatasetProcessing",
                        ["OperationId"] = operationId,
                        ["Status"] = "Success"
                    },
                    new Dictionary<string, double>
                    {
                        ["DurationMs"] = duration,
                        ["RecordsProcessed"] = 1500
                    });
            }
            catch (Exception ex)
            {
                // Track the exception with context
                _telemetryClient.TrackException(ex, "ProcessLargeDataset", new Dictionary<string, string>
                {
                    ["OperationId"] = operationId,
                    ["DatasetPath"] = datasetPath,
                    ["ProcessingStage"] = "DataTransformation"
                });
                
                // Track failed operation
                var duration = (DateTime.UtcNow - startTime).TotalMilliseconds;
                _telemetryClient.TrackEvent("OperationFailed", 
                    new Dictionary<string, string>
                    {
                        ["OperationType"] = "DatasetProcessing",
                        ["OperationId"] = operationId,
                        ["ErrorType"] = ex.GetType().Name
                    },
                    new Dictionary<string, double>
                    {
                        ["DurationMs"] = duration
                    });
                
                throw; // Re-throw the exception
            }
        }
        
        public void Dispose()
        {
            _telemetryClient?.Flush();
            _telemetryClient?.Dispose();
        }
    }
}
