using System.ComponentModel.DataAnnotations;

namespace BecaTracktion.Models.PowerApps
{
    /// <summary>
    /// Request model for client registration from PowerApps
    /// This matches the PowerApps form fields exactly
    /// </summary>
    public class ClientRegistrationRequest
    {
        /// <summary>
        /// Name of the client application (required)
        /// </summary>
        [Required(ErrorMessage = "Application name is required")]
        [StringLength(200, MinimumLength = 3, ErrorMessage = "Application name must be between 3 and 200 characters")]
        [Display(Name = "Application Name")]
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// Description of the client application
        /// </summary>
        [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
        [Display(Name = "Application Description")]
        public string? ClientDescription { get; set; }

        /// <summary>
        /// Type of application (Desktop, Web, Mobile, Service)
        /// </summary>
        [Required(ErrorMessage = "Application type is required")]
        [RegularExpression("^(Desktop|Web|Mobile|Service)$", ErrorMessage = "Application type must be Desktop, Web, Mobile, or Service")]
        [Display(Name = "Application Type")]
        public string ApplicationType { get; set; } = string.Empty;

        /// <summary>
        /// Employee email who is requesting the registration (from SharePoint context)
        /// </summary>
        [Required(ErrorMessage = "Requester email is required")]
        [EmailAddress(ErrorMessage = "Invalid email address")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        [Display(Name = "Requested By")]
        public string RequestedBy { get; set; } = string.Empty;

        /// <summary>
        /// Business justification for the client application
        /// </summary>
        [Required(ErrorMessage = "Business justification is required")]
        [StringLength(1000, MinimumLength = 10, ErrorMessage = "Business justification must be between 10 and 1000 characters")]
        [Display(Name = "Business Justification")]
        public string BusinessJustification { get; set; } = string.Empty;

        /// <summary>
        /// Expected monthly usage volume (Low, Medium, High, Enterprise)
        /// </summary>
        [Required(ErrorMessage = "Expected monthly volume is required")]
        [RegularExpression("^(Low|Medium|High|Enterprise)$", ErrorMessage = "Expected volume must be Low, Medium, High, or Enterprise")]
        [Display(Name = "Expected Monthly Volume")]
        public string ExpectedMonthlyVolume { get; set; } = string.Empty;

        /// <summary>
        /// Contact email for the client application
        /// </summary>
        [Required(ErrorMessage = "Contact email is required")]
        [EmailAddress(ErrorMessage = "Invalid contact email address")]
        [StringLength(200, ErrorMessage = "Contact email cannot exceed 200 characters")]
        [Display(Name = "Contact Email")]
        public string ContactEmail { get; set; } = string.Empty;

        /// <summary>
        /// Additional comments or notes (optional)
        /// </summary>
        [StringLength(500, ErrorMessage = "Additional comments cannot exceed 500 characters")]
        [Display(Name = "Additional Comments")]
        public string? AdditionalComments { get; set; }
    }

    /// <summary>
    /// Response model for client registration submission
    /// </summary>
    public class ClientRegistrationResponse
    {
        /// <summary>
        /// Unique identifier for the registration request
        /// </summary>
        public Guid RegistrationId { get; set; }

        /// <summary>
        /// Status of the registration request
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Message describing the result
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// When the registration was submitted
        /// </summary>
        public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Expected processing time in business days
        /// </summary>
        public int ExpectedProcessingDays { get; set; } = 3;

        /// <summary>
        /// Reference number for tracking
        /// </summary>
        public string ReferenceNumber => $"REG-{RegistrationId.ToString("N")[..8].ToUpper()}";
    }

    /// <summary>
    /// Model for client approval from PowerApps admin interface
    /// </summary>
    public class ClientApprovalRequest
    {
        /// <summary>
        /// Registration ID to approve
        /// </summary>
        [Required]
        public Guid RegistrationId { get; set; }

        /// <summary>
        /// Admin who is approving the request
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string ApprovedBy { get; set; } = string.Empty;

        /// <summary>
        /// Optional comments from the approver
        /// </summary>
        [StringLength(1000)]
        public string? ReviewComments { get; set; }

        /// <summary>
        /// Optional expiration date for the client credentials
        /// </summary>
        public DateTime? ExpiresAt { get; set; }
    }

    /// <summary>
    /// Response model for client approval
    /// </summary>
    public class ClientApprovalResponse
    {
        /// <summary>
        /// Generated Client ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Generated Client Secret (display once only)
        /// </summary>
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// Status of the approval
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Message describing the result
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// When the approval was processed
        /// </summary>
        public DateTime ApprovedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Link to integration documentation
        /// </summary>
        public string DocumentationUrl { get; set; } = "/swagger";

        /// <summary>
        /// API endpoint for token requests
        /// </summary>
        public string TokenEndpoint { get; set; } = "/api/auth/token";
    }

    /// <summary>
    /// Model for client rejection from PowerApps admin interface
    /// </summary>
    public class ClientRejectionRequest
    {
        /// <summary>
        /// Registration ID to reject
        /// </summary>
        [Required]
        public Guid RegistrationId { get; set; }

        /// <summary>
        /// Admin who is rejecting the request
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string RejectedBy { get; set; } = string.Empty;

        /// <summary>
        /// Required reason for rejection
        /// </summary>
        [Required]
        [StringLength(1000, MinimumLength = 10)]
        public string ReviewComments { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for client rejection
    /// </summary>
    public class ClientRejectionResponse
    {
        /// <summary>
        /// Status of the rejection
        /// </summary>
        public string Status { get; set; } = "Rejected";

        /// <summary>
        /// Message describing the result
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// When the rejection was processed
        /// </summary>
        public DateTime RejectedAt { get; set; } = DateTime.UtcNow;
    }
}
