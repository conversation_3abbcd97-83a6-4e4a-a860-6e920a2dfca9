namespace BecaTracktion.Models
{
    /// <summary>
    /// Response model for JWT token issuance
    /// </summary>
    public class TokenResponse
    {
        /// <summary>
        /// JWT access token
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// Token type (always "Bearer")
        /// </summary>
        public string TokenType { get; set; } = "Bearer";

        /// <summary>
        /// Token expiration time in seconds
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// Timestamp when the token was issued
        /// </summary>
        public DateTime IssuedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when the token expires
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Client ID for which the token was issued
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if this is a first-time registration
        /// </summary>
        public bool IsFirstRegistration { get; set; }
    }
}
