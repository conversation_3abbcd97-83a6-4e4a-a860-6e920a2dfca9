# BecaTracktion Security Implementation

## Overview

The BecaTracktion Telemetry Microservice now implements JWT-based authentication with first-run app verification to prevent unauthorized access and ensure token binding to specific applications and machines.

## Security Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                         Client Application                          │
│  ┌─────────────────────────────────────────────────────────────┐    │
│  │                First-Run Registration                        │    │
│  │  1. Collect App Metadata (GUID, Name)                      │    │
│  │  2. Send Token Request with Credentials + Metadata         │    │
│  │  3. Receive JWT Token with Embedded Metadata               │    │
│  └─────────────────────────────────────────────────────────────┘    │
│                                │                                     │
│  ┌─────────────────────────────▼─────────────────────────────────┐   │
│  │              Subsequent Requests                             │   │
│  │  1. Include JWT Token in Authorization Header               │   │
│  │  2. Include App Metadata in Request Headers                 │   │
│  │  3. Server Validates Token + Metadata Binding               │   │
│  └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                    BecaTracktion Microservice                       │
│  ┌─────────────────────────────────────────────────────────────┐    │
│  │                Authentication Flow                           │    │
│  │  1. Validate Client Credentials                             │    │
│  │  2. Register/Update App Metadata                            │    │
│  │  3. Generate JWT with Embedded Metadata                     │    │
│  │  4. Return Token Response                                   │    │
│  └─────────────────────────────────────────────────────────────┘    │
│                                │                                     │
│  ┌─────────────────────────────▼─────────────────────────────────┐   │
│  │              Request Validation                              │   │
│  │  1. Validate JWT Token Signature & Expiration               │   │
│  │  2. Extract Metadata from Token Claims                      │   │
│  │  3. Compare with Request Metadata Headers                   │   │
│  │  4. Reject if Metadata Doesn't Match                        │   │
│  └─────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────┘
```

## Security Features

### 1. JWT Token Authentication
- **Token Issuance**: Clients must authenticate with valid ClientId and ClientSecret
- **Token Expiration**: Tokens expire after 1 hour (configurable)
- **Token Validation**: All telemetry endpoints require valid JWT tokens
- **Role-Based Access**: Tokens include role claims for authorization

### 2. First-Run App Verification
- **App Metadata Collection**: Clients collect unique app identifiers on first run
- **Metadata Binding**: App metadata is embedded in JWT token claims
- **Registration Tracking**: Server tracks first-time vs. returning app registrations

### 3. Token Binding Validation
- **Metadata Matching**: Every request validates token metadata against current context
- **App Binding**: Tokens are bound to specific application GUIDs
- **Request Rejection**: Mismatched metadata results in 401 Unauthorized

## App Metadata Components

### Required Metadata
- **App GUID**: Unique identifier for the application instance
- **App Name**: Human-readable application name

### Optional Metadata
- **App Version**: Application version string
- **Operating System**: OS information
- **Additional Properties**: Custom key-value pairs

## Authentication Flow

### 1. Token Request
```http
POST /api/auth/token
Content-Type: application/json

{
  "clientId": "revit-addin-client",
  "clientSecret": "revit-secret-2024",
  "appMetadata": {
    "appGuid": "550e8400-e29b-41d4-a716-************",
    "appName": "Revit Add-in",
    "appVersion": "1.0.0",
    "operatingSystem": "Windows 11"
  },
  "expirationMinutes": 60
}
```

### 2. Token Response
```http
HTTP/1.1 200 OK
Content-Type: application/json

{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "issuedAt": "2024-01-15T10:30:00Z",
  "expiresAt": "2024-01-15T11:30:00Z",
  "clientId": "revit-addin-client",
  "isFirstRegistration": true
}
```

### 3. Authenticated Telemetry Request
```http
POST /api/telemetry/event
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-App-Guid: 550e8400-e29b-41d4-a716-************
X-App-Name: Revit Add-in
X-App-Version: 1.0.0
X-Operating-System: Windows 11
Content-Type: application/json

{
  "eventName": "Command Executed",
  "properties": {
    "command": "CreateWall"
  }
}
```

## Client Implementation

### C# Client Usage
```csharp
// Initialize client with credentials
var client = new TracktionClient(
    baseUrl: "https://tracktion.azurewebsites.net",
    clientId: "revit-addin-client",
    clientSecret: "revit-secret-2024",
    appName: "My Revit Add-in"
);

// Track events (authentication handled automatically)
await client.TrackEventAsync("User Action", new Dictionary<string, string>
{
    ["action"] = "button_click",
    ["feature"] = "wall_tool"
});
```

### Authentication Handling
- **Automatic Token Management**: Client automatically requests and refreshes tokens
- **Metadata Collection**: App metadata is collected automatically on initialization
- **Header Injection**: Authentication and metadata headers are added to all requests
- **Error Handling**: Authentication failures are logged and handled gracefully

## Security Configuration

### JWT Settings (appsettings.json)
```json
{
  "Jwt": {
    "SecretKey": "Your-Super-Secret-Key-Change-In-Production",
    "Issuer": "BecaTracktion",
    "Audience": "BecaTracktion-Clients",
    "ExpirationMinutes": 60
  }
}
```

### Default Client Credentials
For development and testing, the following clients are pre-configured:

| Client ID | Secret | Description |
|-----------|--------|-------------|
| `revit-addin-client` | `revit-secret-2024` | Revit Add-in Client |
| `python-script-client` | `python-secret-2024` | Python Script Client |
| `test-client` | `test-secret-2024` | Test Client |

**⚠️ Important**: Change these credentials in production environments.

## Security Best Practices

### Production Deployment
1. **Change Default Secrets**: Update all client secrets and JWT signing key
2. **Use Environment Variables**: Store secrets in environment variables or Azure Key Vault
3. **Enable HTTPS Only**: Disable HTTP endpoints in production
4. **Restrict CORS**: Limit CORS to specific origins
5. **Implement Rate Limiting**: Add rate limiting to prevent abuse
6. **Monitor Authentication**: Log and monitor authentication attempts

### Client Security
1. **Secure Credential Storage**: Store client credentials securely
2. **Token Caching**: Cache tokens securely and refresh before expiration
3. **Error Handling**: Handle authentication errors gracefully
4. **Logging**: Log authentication events for debugging

### Network Security
1. **TLS/SSL**: Always use HTTPS for token requests and telemetry
2. **Certificate Validation**: Validate server certificates
3. **Network Isolation**: Use private networks where possible

## Troubleshooting

### Common Authentication Errors

#### 401 Unauthorized - Invalid Credentials
```json
{
  "error": "invalid_client",
  "error_description": "Invalid client credentials"
}
```
**Solution**: Verify ClientId and ClientSecret are correct.

#### 401 Unauthorized - Token Binding Failed
```json
{
  "error": "unauthorized",
  "error_description": "Token binding validation failed"
}
```
**Solution**: Ensure app metadata in request matches token metadata.

#### 401 Unauthorized - Token Expired
**Solution**: Client should automatically refresh token. Check token expiration handling.

### Debugging Tips
1. **Check Token Claims**: Use `/api/auth/validate` endpoint to inspect token claims
2. **Verify Metadata**: Ensure app metadata is consistent across requests
3. **Monitor Logs**: Check server logs for detailed error information
4. **Test Authentication**: Use `/api/auth/clients` to see available clients (development only)

## Migration from Unsecured Version

### Server-Side Changes
1. All telemetry endpoints now require authentication
2. New authentication endpoints added (`/api/auth/token`, `/api/auth/validate`)
3. Token binding validation middleware added

### Client-Side Changes
1. Update client initialization to include credentials
2. Replace direct HTTP calls with authenticated client methods
3. Handle authentication errors appropriately

### Backward Compatibility
- **Breaking Change**: Unsecured telemetry requests will return 401 Unauthorized
- **Migration Required**: All clients must be updated to use authentication
- **Testing**: Use provided test credentials for development and testing
