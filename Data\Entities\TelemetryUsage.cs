using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BecaTracktion.Data.Entities
{
    /// <summary>
    /// Entity representing telemetry usage statistics
    /// </summary>
    [Table("TelemetryUsage")]
    public class TelemetryUsage
    {
        /// <summary>
        /// Unique identifier for this usage record
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        /// <summary>
        /// Client ID that generated the telemetry
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// App GUID that generated the telemetry (optional)
        /// </summary>
        [StringLength(50)]
        public string? AppGuid { get; set; }

        /// <summary>
        /// Type of telemetry event (event, exception, metric, pageview)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// Number of events of this type on this date
        /// </summary>
        public int EventCount { get; set; } = 1;

        /// <summary>
        /// Date of the usage (for daily aggregation)
        /// </summary>
        [Column(TypeName = "date")]
        public DateTime UsageDate { get; set; }

        /// <summary>
        /// When this record was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        /// <summary>
        /// The client application that generated this usage
        /// </summary>
        [ForeignKey(nameof(ClientId))]
        public virtual ClientApplication ClientApplication { get; set; } = null!;
    }
}
